<view class="container">
  <!-- 头像背景 -->
  <view class="main-banner"></view>

  <!-- 手机号输入对话框 -->
  <view class="phone-dialog {{ showPhoneDialog ? 'show' : '' }}">
    <view class="dialog-content">
      <view class="dialog-title">请先绑定手机</view>
      <input type="number"
             class="phone-input"
             maxlength="11"
             placeholder="请输入11位手机号"
             bindinput="onPhoneInput"
             value="{{ inputPhone }}" />
      <view class="dialog-desc">此手机号将作为观展时的验票凭证，请勿填错</view>
      <view class="dialog-buttons">
        <button class="btn cancel" bindtap="closePhoneDialog">取消</button>
        <button class="btn {{ isValidPhone ? '' : 'disabled' }}"
                bindtap="submitPhone"
                disabled="{{ !isValidPhone }}">确定</button>
      </view>
    </view>
  </view>

  <!-- 菜单区 -->
  <view class="main-card">
    <!-- 个人资料区 -->
    <view class="profile-section">
      <view wx:if="{{ avatar }}" class="avatar-wrapper">
        <image class="avatar" src="{{ avatar }}"></image>
      </view>
      <view wx:else class="avatar-wrapper" bindtap="getUserProfile">
        <image class="default-avatar" src="/assets/icons/user-solid.svg"></image>
      </view>

      <text class="nickname">{{ nickname }}</text>
    </view>

    <view class="menu-section">
      <view wx:if="{{ !phone }}" class="menu-item" >
        <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
          绑定手机
        </button>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToUserOrders">
        <text>我的订单</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToThirdpartyOrders">
        <text>渠道预约订单</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToUserNotice">
        <text>用户须知</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToUserPrivacy">
        <text>隐私政策</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view wx:if="{{ avatar || phone }}" class="menu-item" bindtap="userLogout">
        <text>退出</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
    </view>
  </view>
</view>
