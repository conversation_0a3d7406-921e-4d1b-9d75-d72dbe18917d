@import '/app.wxss';

.container {
  padding-top: 150rpx;
}

/* 标题 */
.custom-navbar {
  left: 0;
  position: absolute;
  display: flex;
  width: 100%;
  align-items: center;
  padding-left: 25rpx;
  box-sizing: border-box;
  z-index: 1000;
  background-color: transparent;
  transition: background-color 0.3s;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.logo {
  width: calc(40rpx * 4.58);
  height: 40rpx;
}

/* 1. 轮播区域 */
.swiper-container {
  width: 100%;
  height: 485rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

/* 轮播图片 */
.swiper-image {
  width: 100%;
  height: 100%;
}

/* 调整轮小圆点 */
.swiper-container .wx-swiper-dots {
  bottom: 100rpx;
}

/* 2. 导航区 */
.nav-section {
  display: flex;
  position: relative;
  justify-content: space-around;
  width: 720rpx;
  padding: 30rpx;
  margin-top: 250rpx;
  background-color: white;
  z-index: 10;
  box-sizing: border-box;
  border-radius: 35rpx;
  box-shadow: 0 0 10rpx var(--shadow);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: var(--dark);
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

/* 3. 热展推荐区 */
.hot-exhibitions-section {
  width: 720rpx;
  margin: 10rpx auto;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
}

.section-more {
  font-size: 24rpx;
  color: var(--light);
}

.exhibition-list {
  display: flex;
  flex-direction: column;
}

.exhibition-item {
  overflow: hidden;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 0 10rpx var(--shadow);
}

.exhibition-image {
  /* 宽高比: 1.6 */
  width: 100%;
  height: 450rpx;
  display: block;
}
