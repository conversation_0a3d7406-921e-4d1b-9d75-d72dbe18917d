// pages/home/<USER>/brand.js
import api from '../../../utils/http';

Page({
  data: {
    brandIntro: '',
    brandBG: ''
  },

  onLoad(options) {
    this.loadBrandIntro();
  },

  async loadBrandIntro() {
    try {
      const settings = await api.getSettings();
      if (settings) {
        if (settings.brand_bg) this.setData({ brandBG: settings.brand_bg });
        if (settings.brand_intro) {
          // 处理文本，在每个段落前添加缩进
          const formattedText = settings.brand_intro
            .split('\n')
            .map((paragraph) => paragraph.trim())
            .map((paragraph) => '&emsp;&emsp;' + paragraph) // 添加缩进
            .join('\n'); // 重新组合文本

          this.setData({
            brandIntro: formattedText
          });
        }
      }
    } catch (error) {
      console.error('获取品牌介绍失败:', error);
      wx.showToast({
        title: '获取品牌介绍失败',
        icon: 'none'
      });
    }
  }
});
