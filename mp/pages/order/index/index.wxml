<view class="container">
  <!-- 主导航：普通订单/联票订单 -->
  <view class="tabs-container main-tabs">
    <view class="tab-item {{activeTab === 'normal' ? 'active' : ''}}"
          data-tab="normal"
          bindtap="switchMainTab">
      普通订单
      <view class="active-line" wx:if="{{activeTab === 'normal'}}"></view>
    </view>
    <view class="tab-item {{activeTab === 'joint' ? 'active' : ''}}"
          data-tab="joint"
          bindtap="switchMainTab">
      联票订单
      <view class="active-line" wx:if="{{activeTab === 'joint'}}"></view>
    </view>
  </view>

  <!-- 子导航：订单状态 -->
  <scroll-view enable-flex="true" scroll-x class="filter-scroll">
    <view class="order-filters">
      <view wx:for="{{statusTabs}}"
            wx:key="item"
            class="filter-status {{activeStatus === item ? 'active' : ''}}"
            data-status="{{item}}"
            bindtap="switchStatusTab">
        {{item}}
      </view>
    </view>
  </scroll-view>

  <!-- 订单列表 -->
  <view wx:if="{{isLoadingMore}}" class="load-more-hint">正在加载 ...</view>

  <block wx:elif="{{ orderList.length > 0 }}">
    <view class="order-list">
      <view class="order-card"
            wx:for="{{orderList}}"
            wx:key="id"
            data-order-id="{{item.id}}"
            bindtap="navigateToDetail">
        <view class="order-card-header">
          <view class="order-title">{{item.title}}</view>
          <view class="order-status order-status-{{statuses[item.status]}}">
            {{item.status}}
          </view>
        </view>
        <view class="order-card-body">
          <image class="item-thumbnail" src="{{item.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
          <view class="order-info">
            <view class="info-item" wx:if="{{item.address}}">地址: {{item.address}}</view>
            <view class="info-item" wx:if="{{item.grade}}">票名: {{item.grade}} {{item.timeslot}}</view>
            <view class="info-item" wx:if="{{item.quantity}}">数量: {{item.quantity}}</view>
            <view class="price-label">
              <text>总价</text>
              <text class="price-value">¥{{item.amount}}</text>
            </view>
          </view>
        </view>
        <view class="order-card-footer">
          <view class="left-section">
            <view class="expire-time"
                  wx:if="{{item.status === '待支付' && item.expireTimeStr}}">
              订单将于{{item.expireTimeStr}}失效
            </view>
          </view>

          <block wx:if="{{item.status === '待支付'}}">
            <button class="btn btn-cancel"
                    catchtap="handleCancelOrder"
                    data-order-id="{{item.id}}">
              取消订单
            </button>
            <button class="btn btn-pay"
                    catchtap="handlePayOrder"
                    data-order-id="{{item.id}}">
              立即支付
            </button>
          </block>

          <block wx:elif="{{item.status === '待使用'}}">
            <button class="btn btn-cancel"
                      catchtap="handleRefundOrder"
                      data-order-id="{{item.id}}">
              申请退款
            </button>
            <button class="btn btn-pay"
                      catchtap="navigateToDetail"
                      data-order-id="{{item.id}}">
              立即使用
            </button>
          </block>

          <block wx:else>
            <button class="btn btn-delete"
                    catchtap="handleDeleteOrder"
                    data-order-id="{{item.id}}">
              删除订单
            </button>
          </block>
        </view>
      </view>
    </view>
  </block>

  <view wx:else class="empty">
    <image class="empty-image" src="/assets/empty.png" mode="aspectFit"></image>
    <text>暂无相关订单</text>
  </view>

  <!-- 加载更多提示 -->
  <view class="load-more-hint" wx:if="{{!hasMoreData && orderList.length > 0}}">没有更多数据了</view>
</view>
