import api from '../../../utils/http';
import utils from '../../../utils/util';

Page({
  data: {
    activeTab: 'normal', // 可选项: normal, joint
    activeStatus: '全部',
    statusTabs: ['全部', '未支付', '待使用', '已使用', '退款'],
    statuses: {
      待支付: 'pending',
      支付失败: 'failed',
      待使用: 'paid',
      已使用: 'used',
      已取消: 'canceled',
      已关闭: 'closed',
      退款中: 'refunding',
      已退款: 'refunded'
    },
    catgList: [], // 订单类型列表，后端 catg 字段
    activeCatg: '', // 当前选中的 catg
    orderList: [],
    pageNum: 1,
    pageSize: 60,
    isLoadingMore: false,
    hasMoreData: true
  },

  onLoad: async function (options) {
    await this.initCatgList();
    this.loadOrders(true);
  },

  // 初始化订单类型列表
  initCatgList: async function () {
    try {
      const res = await api.getOrderList();
      const catgSet = new Set();
      (Array.isArray(res) ? res : []).forEach((item) => {
        if (item.catg) catgSet.add(item.catg);
      });
      const catgArr = Array.from(catgSet);
      this.setData({
        catgList: catgArr,
        activeCatg: catgArr[0] || ''
      });
    } catch (e) {
      wx.showToast({ title: '订单类型加载失败', icon: 'none' });
    }
  },

  // 主导航切换
  switchMainTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({
        activeTab: tab,
        activeStatus: '全部',
        orderList: [],
        pageNum: 1,
        hasMoreData: true
      });
      this.loadOrders(true);
    }
  },

  // 子导航切换
  switchStatusTab: function (e) {
    const status = e.currentTarget.dataset.status;
    if (status !== this.data.activeStatus) {
      this.setData({
        activeStatus: status,
        orderList: [],
        pageNum: 1,
        hasMoreData: true
      });
      this.loadOrders(true);
    }
  },

  // 切换订单类型
  switchTab: function (e) {
    const newCatg = e.currentTarget.dataset.catg;
    if (newCatg !== this.data.activeCatg) {
      this.setData({
        activeCatg: newCatg,
        pageNum: 1,
        orderList: [],
        hasMoreData: true
      });
      this.loadOrders(true);
    }
  },

  // 加载订单数据
  loadOrders: async function (isRefresh = false) {
    if (this.data.isLoadingMore && !isRefresh) return;
    this.setData({ isLoadingMore: true });
    const { activeTab, activeStatus, pageNum, pageSize } = this.data;
    const is_joint = activeTab === 'joint';
    const status = activeStatus === '全部' ? null : activeStatus;
    const page = Number(pageNum); // 确保 page 为整型
    try {
      const res = await api.getOrderList(is_joint, status, page);
      let orders = Array.isArray(res) ? res : [];
      orders = orders.map((order) => ({
        id: order.id,
        title: order.title,
        status: order.status,
        thumbnail: order.thumbnail,
        grade: order.grade,
        timeslot: order.timeslot,
        amount: order.amount,
        expireTimeStr: order.expire_time ? formatExpireTime(order.expire_time) : '',
        catg: order.catg,
        address: order.address,
        hall: order.hall,
        quantity: order.quantity,
        phone: order.phone
      }));
      const hasMore = orders.length >= pageSize;
      this.setData({
        orderList: isRefresh ? orders : this.data.orderList.concat(orders),
        pageNum: page + 1,
        hasMoreData: hasMore
      });
    } catch (err) {
      wx.showToast({ title: '订单加载失败', icon: 'none' });
    }
    this.setData({ isLoadingMore: false });
    if (isRefresh) wx.stopPullDownRefresh();
  },

  onReachBottom: function () {
    if (this.data.hasMoreData && !this.data.isLoadingMore) {
      this.loadOrders();
    }
  },

  onPullDownRefresh: function () {
    this.setData({ pageNum: 1, hasMoreData: true });
    this.loadOrders(true);
  },

  // 立即支付
  handlePayOrder: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    // 跳转到支付页面或调起微信支付API
    api.continue2Pay(orderId).then((paymentArgs) => {
      wx.requestPayment({
        timeStamp: paymentArgs.timeStamp,
        nonceStr: paymentArgs.nonceStr,
        package: paymentArgs.package,
        signType: paymentArgs.signType,
        paySign: paymentArgs.paySign,
        success(res) {
          wx.showToast({ title: '支付成功！', icon: 'success' });
          setTimeout(() => {
            wx.redirectTo({ url: '/pages/order/detail/detail?orderId=' + orderId });
          }, 2000);
        },
        fail(err) {
          wx.showToast({ title: '支付失败！', icon: 'error' });
        }
      });
    });
  },

  handleCancelOrder: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '取消订单',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          api.cancelOrder(orderId).then(() => {
            utils.reloadCurrentPage();
          });
        }
      }
    });
  },

  // 退款
  handleRefundOrder: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    api.refundOrder(orderId).then(() => {
      utils.reloadCurrentPage();
    });
  },

  // 删除订单
  handleDeleteOrder: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '删除订单',
      content: '删除后不可恢复！确定要删除该订单吗？',
      confirmColor: '#ec536a', // 警示性颜色 (softred)
      success: (res) => {
        if (res.confirm) {
          api.deleteOrder(orderId).then(() => {
            utils.reloadCurrentPage();
          });
        }
      }
    });
  },

  // 跳转到订单详情页面
  navigateToDetail: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    if (orderId) {
      wx.navigateTo({
        url: `/pages/order/detail/detail?orderId=${orderId}`
      });
    }
  }
});

// 工具函数：格式化后端 expire_time 字段为 HH时mm分
function formatExpireTime(isoTimeStr) {
  if (!isoTimeStr) return '';
  const date = new Date(isoTimeStr.replace(/-/g, '/'));
  const hours = date.getHours();
  const minutes = date.getMinutes();
  return `${hours < 10 ? '0' : ''}${hours}时${minutes < 10 ? '0' : ''}${minutes}分`;
}
