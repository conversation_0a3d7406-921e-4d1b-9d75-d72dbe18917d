@import '/app.wxss';

/* Tabs 切换栏 */
.tabs-container {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 10;
  width: 100%;
  height: 2.5rem;
  justify-content: space-around;
  align-items: center;
  color: white;
  background-color: var(--primary);
}

.tab-item {
  position: relative;
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  line-height: 2.6rem;
  color: ver(--silver);
  transition: color 0.3s;
  box-sizing: border-box;
}

.tab-item.active {
  font-weight: bold;
  color: white;
}

.active-line {
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 50rpx;
  height: 6rpx;
  background-color: white;
  border-radius: 6rpx;
}

/* 筛选栏滚动容器 */
.filter-scroll {
  /* flex: 1;  */
  background-color: white;
  box-sizing: border-box;
  width: 100%;
}

/* 筛选按钮容器 */
.order-filters {
  display: flex;
  flex-direction: row;
  padding: 20rpx;
  white-space: nowrap;
}

.filter-status {
  flex-shrink: 0;
  margin-right: 20rpx;
  padding: 5rpx 30rpx;
  font-size: 26rpx;
  border-radius: 50rpx;
  color: var(--dark);
  background-color: var(--silver);
  transition: all 0.3s ease;
}

.filter-status:last-child {
  margin-right: 0;
}

.filter-status.active {
  color: white;
  background-color: var(--primary);
  font-weight: bold;
}

/* 订单列表 */
.order-list {
  display: block;
  width: 720rpx;
}

.order-card {
  background-color: white;
  padding: 25rpx;
  margin-bottom: 20rpx;
  border-radius: 15rpx;
  box-shadow: 0 0 10rpx var(--shadow);
}

.order-list>.order-card:first-child {
  margin-top: 20rpx;
}

.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-title {
  flex: 1;
  font-size: 28rpx;
  margin-right: 10rpx;
  font-weight: bold;
  color: var(--dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-status {
  padding: 6rpx 24rpx;
  font-size: 24rpx;
  border-radius: 50rpx;
  color: var(--dark);
  background-color: var(--silver);
  transition: all 0.3s ease;
}

.order-status-pending {
  color: var(--dark);
  background-color: rgb(255, 196, 0);
}

.order-status-paid {
  color: white;
  background-color: rgb(0, 192, 0);
}

.order-status-used {
  color: white;
  background-color: black;
}

.order-status-failed,
.order-status-canceled,
.order-status-closed,
.order-status-refunding,
.order-status-refunded {
  color: var(--dark);
  background-color: var(--bright);
}

.order-card-body {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid var(--silver);
}

.order-card-body:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.order-info {
  display: flex;
  flex: 1;
  font-size: 26rpx;
  color: var(--medium);
  flex-direction: column;
  justify-content: flex-start;
  margin-left: 20rpx;
}

.info-item {
  margin-bottom: 10rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.sub-ticket-title {
  font-size: 26rpx;
  font-weight: bold;
  color: var(--dark);
}

.price-label {
  margin: 15rpx 0;
  text-align: right;
  font-size: 28rpx;
  color: var(--dark);
}

.price-value {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--softred);
  margin-left: 8rpx;
}

.order-card-footer {
  display: flex;
  margin-top: 25rpx;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}

.left-section {
  display: flex;
  flex: 1;
  white-space: nowrap;
}

.expire-time {
  font-size: 26rpx;
  color: var(--softred);
}

.btn {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  max-width: 180rpx;
}

.btn-pay {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-cancel {
  color: var(--primary);
  background-color: white;
  border-color: var(--primary);
}

.btn-delete {
  color: var(--softred);
  background-color: transparent;
  border-color: var(--softred);
}

.btn-cancel:active,
.btn-delete:active {
  opacity: 0.5;
  background-color: white;
}

/* 空状态 */
.empty-orders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999999;
  font-size: 28rpx;
  width: 100%;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

/* 加载更多 */
.load-more-hint {
  text-align: center;
  padding: 50rpx 0;
  color: #999999;
  font-size: 30rpx;
}
