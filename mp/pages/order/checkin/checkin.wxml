<view class="container">

  <block wx:if="{{ isAdm }}">
  <view class="scan-wrapper">
    <image src="/assets/icons/qrcode.svg" class="scan-icon" mode="aspectFill"></image>
    <button bindtap="scanCode" class="btn scan-btn">扫码检票</button>
  </view>
  </block>

  <block wx:else>
    <!-- 主导航：普通订单/联票订单 -->
    <view class="tabs-container main-tabs">
      <view class="tab-item {{activeTab === 'normal' ? 'active' : ''}}"
            data-tab="normal"
            bindtap="switchMainTab">
        普通订单
        <view class="active-line" wx:if="{{activeTab === 'normal'}}"></view>
      </view>
      <view class="tab-item {{activeTab === 'joint' ? 'active' : ''}}"
            data-tab="joint"
            bindtap="switchMainTab">
        联票订单
        <view class="active-line" wx:if="{{activeTab === 'joint'}}"></view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view wx:if="{{isLoadingMore}}" class="load-more-hint">正在加载 ...</view>
    <block wx:elif="{{orderList.length > 0}}">
      <view class="order-list">
        <view class="order-card"
              wx:for="{{orderList}}"
              wx:key="id"
              data-order-id="{{item.id}}"
              bindtap="navigateToDetail">
          <view class="order-card-header">
            <view class="order-title">{{item.title}}</view>
            <view class="order-status order-status-paid">
              {{item.status}}
            </view>
          </view>
          <view class="order-card-body">
            <image class="item-thumbnail" src="{{item.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
            <view class="order-info">
              <view class="info-item" wx:if="{{item.address}}">地址：{{item.address}}</view>
              <view class="info-item" wx:if="{{item.grade}}">票名：{{item.grade}} {{item.timeslot}}</view>
              <view class="info-item" wx:if="{{item.quantity}}">数量：{{item.quantity}}</view>
              <view class="price-label">
                <text>总价</text>
                <text class="price-value">¥{{item.amount}}</text>
              </view>
            </view>
          </view>
          <view class="order-card-footer">
            <view class="left-section"></view>
            <button class="btn btn-pay"
                    catchtap="navigateToDetail"
                    data-order-id="{{item.id}}">
                检票
            </button>
          </view>
        </view>
      </view>
    </block>

    <view wx:else class="empty">
      <image class="empty-image" src="/assets/empty.png" mode="aspectFit"></image>
      <text>暂无未使用的票券</text>
    </view>

    <!-- 加载更多提示 -->
    <view class="load-more-hint" wx:if="{{!hasMoreData && orderList.length > 0}}">没有更多数据了</view>
  </block>
</view>
