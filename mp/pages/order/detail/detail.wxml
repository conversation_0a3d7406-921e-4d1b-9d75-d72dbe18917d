<view class="detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text>加载中...</text>
  </view>

  <!-- 订单详情内容 -->
  <view wx:else class="detail-content">
    <!-- 票券容器 -->
    <view class="ticket-container">
      <!-- 订单信息卡片 -->
      <view class="order-info-card">
        <view class="order-header">
          <view class="order-title">{{detail.title}}</view>

          <view wx:if="{{ checkin === 'success' }}" class="order-status order-status-paid">核验通过</view>
          <view wx:elif="{{ detail.status === '待使用' }}" class="order-status order-status-paid">待使用</view>
          <view wx:else class="order-status order-status-refunded">{{detail.status}}</view>
        </view>

        <view class="order-body">
          <image class="item-thumbnail" src="{{detail.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
          <view class="order-info">
            <view class="info-item">展期：{{period}}</view>
            <view class="info-item">票名：{{detail.grade}} {{detail.timeslot}}</view>
            <view class="info-item">数量：{{detail.quantity}}</view>
            <view class="price-section">
              <text class="price-label">总价</text>
              <text class="price-symbol">¥</text>
              <text class="price-value">{{detail.amount}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 撕裂线分割 -->
      <view class="tear-line">
        <view class="tear-notch tear-left"></view>
        <view class="tear-dashes"></view>
        <view class="tear-notch tear-right"></view>
      </view>

      <!-- 电子票区域 -->
      <view class="eticket-section">
        <view class="section-title">电子票</view>
        <view class="qrcode-container">
          <!-- 二维码占位区域 -->
          <view class="qrcode-placeholder">
            <canvas canvas-id="ticketQrcode" style="width: 240rpx; height: 240rpx;"></canvas>
            <image class="qr-icon" src="/assets/favicon.png"></image>
          </view>
          <view wx:if="{{ detail.status === '待使用' || checkin === 'success' }}"
                class="verification-code">
            <text class="code-label">验证码：{{detail.vcode }}</text>
          </view>
        </view>
        <view wx:if="{{ detail.status === '待使用' }}"
              class="checkin-tip">请向门店店员出示该二维码进行核销</view>
      </view>
    </view>

    <!-- 订单信息详情 -->
    <view class="order-details-section">
      <view class="section-title">订单信息</view>
      <view class="details-list">
        <view class="detail-item">
          <text class="detail-label">购票人：</text>
          <text class="detail-value">{{detail.buyer_name || detail.phone }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">购票手机：</text>
          <text class="detail-value">{{detail.phone}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">微信订单号：</text>
          <text class="detail-value">{{detail.wx_transaction_id}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">商户单号：</text>
          <text class="detail-value">{{detail.trade_no}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">下单时间：</text>
          <text class="detail-value">{{detail.created}}</text>
        </view>
        <view class="detail-item" wx:if="{{detail.refund_time}}">
          <text class="detail-label">退款时间：</text>
          <text class="detail-value">{{detail.refund_time}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
