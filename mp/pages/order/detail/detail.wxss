@import '/app.wxss';

page {
  --radius: 25rpx;
}

/* 页面背景色 */
page {
  background-color: var(--primary);
}

/* 详情容器 */
.detail-container {
  background-color: var(--primary);
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  color: var(--medium);
  font-size: 28rpx;
}

/* 详情内容容器 */
.detail-content {
  padding: 15rpx;
}

/* 票券容器 */
.ticket-container {
  background-color: white;
  border-radius: 50rpx 50rpx 30rpx 30rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 0 10rpx var(--shadow);
  position: relative;
}

/* 订单信息卡片 */
.order-info-card {
  background-color: white;
  padding: 30rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
  margin-right: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-status {
  padding: 8rpx 24rpx;
  font-size: 24rpx;
  border-radius: 50rpx;
  color: var(--dark);
  background-color: var(--silver);
}

.order-status-pending {
  color: var(--dark);
  background-color: rgb(255, 196, 0);
}

.order-status-paid {
  color: white;
  background-color: rgb(0, 192, 0);
}

.order-status-used {
  color: white;
  background-color: black;
}

.order-status-refunded {
  color: var(--dark);
  background-color: var(--bright);
}

.order-body {
  display: flex;
  align-items: flex-start;
}

.order-info {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-left: 20rpx;
}

.info-item {
  font-size: 26rpx;
  color: var(--medium);
  margin-bottom: 10rpx;
}

.price-section {
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.price-label {
  font-size: 28rpx;
  color: var(--dark);
  margin-right: 10rpx;
}

.price-symbol {
  font-size: 28rpx;
  color: var(--dark);
  margin-right: 4rpx;
}

.price-value {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--dark);
}

/* 撕裂线效果 */
.tear-line {
  position: relative;
  height: calc(var(--radius) * 2);
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tear-notch {
  position: absolute;
  width: calc(var(--radius) * 2);
  height: calc(var(--radius) * 2);
  background-color: var(--primary);
  border-radius: 50%;
  top: 0;
  z-index: 2;
}

.tear-left {
  left: calc(var(--radius) * -1);
}

.tear-right {
  right: calc(var(--radius) * -1);
}

.tear-dashes {
  width: calc(100% - 60rpx);
  height: 2rpx;
  background-image: repeating-linear-gradient(to right,
      var(--light) 0,
      var(--light) 10rpx,
      transparent 10rpx,
      transparent 20rpx);
  position: relative;
  z-index: 1;
}

/* 电子票区域 */
.eticket-section {
  background-color: white;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
  text-align: center;
  margin-bottom: 25rpx;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 25rpx;
}

.qrcode-placeholder {
  width: 270rpx;
  height: 270rpx;
  background-color: var(--silver);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25rpx;
  position: relative;
}

.qr-icon {
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  padding: 6rpx;
  background-color: white;
  border-radius: 10rpx;
  z-index: 10;
  overflow: hidden;
}

.verification-code {
  display: flex;
  align-items: center;
  padding: 15rpx 30rpx;
  background-color: var(--paper);
  border-radius: 50rpx;
  border: 2rpx solid var(--silver);
}

.code-label {
  font-size: 26rpx;
  color: var(--medium);
  margin-right: 10rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: var(--light);
}

.checkin-tip {
  text-align: center;
  font-size: 26rpx;
  color: var(--medium);
  line-height: 1.6;
}

/* 订单信息详情区域 */
.order-details-section {
  padding: 30rpx;
  margin-bottom: 100rpx;
  background-color: white;
  border-radius: 30rpx;
  box-shadow: 0 0 10rpx var(--shadow);
}

.details-list {
  margin-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15rpx 0;
  border-bottom: 1rpx solid var(--silver);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: var(--medium);
  flex-shrink: 0;
  min-width: 160rpx;
}

.detail-value {
  font-size: 28rpx;
  color: var(--dark);
  text-align: right;
  flex: 1;
  word-break: break-all;
}
