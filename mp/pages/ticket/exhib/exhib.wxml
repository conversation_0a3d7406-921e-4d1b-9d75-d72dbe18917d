<view class="container" wx:if="{{ticketDetail}}">
  <navbar />

  <!-- Banner -->
  <image class="main-banner"
         src="{{ticketDetail.banner}}"
         mode="aspectFill"></image>

  <view class="main-card">
    <view class="section">
      <!-- 票务详情 -->
      <view class="ticket-info-section">
        <view class="ticket-title">{{ticketDetail.title}}</view>
        <view class="ticket-address">地点: {{ticketDetail.addr}}</view>
        <view class="ticket-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{ticketDetail.prices[0]}}</text>
          <text wx:if="{{ticketDetail.prices.length > 1 && ticketDetail.prices[0] !== ticketDetail.prices[1]}}"
                class="price-value"> ~ {{ticketDetail.prices[1]}}</text>
          <text wx:if="{{ticketDetail.prices.length == 1}}" class="price-value"> 起</text>
        </view>
      </view>

      <!-- 退票政策 -->
      <view class="features-section"
            bindtap="goToFeatures">
        <view class="feature-list">
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.refundable ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.refundable ? '无忧退票' : '有条件退票' }}</text>
          </view>
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.source === 'office' ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.source === 'office' ? '官方票' : '第三方票' }}</text>
          </view>
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.electronic ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.electronic ? '电子票' : '纸质票' }}</text>
          </view>
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.invoice ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.invoice ? '客服开发票' : '无法开发票' }}</text>
          </view>
        </view>
        <image class="arrow-icon" src="/assets/icons/right-solid.svg"></image>
      </view>

      <!-- 演出详情标题 -->
      <view class="detail-title-section">
        <view class="section-title">演出详情</view>
      </view>
    </view>

    <view>
      <image wx:for="{{ticketDetail.detail}}" wx:key="*this" src="{{item}}" class="detail-image" mode="widthFix"></image>
    </view>
  </view>

  <!-- 底部购票按钮 -->
  <view class="bottom-actions">
    <button class="btn ar-btn"
            bindtap="goToTicketAR">
      <image class="ar-icon" src="/assets/icons/ar.svg" />
    </button>
    <button class="btn purchase-btn"
            bindtap="goToTicketPurchase">立即购票</button>
  </view>
</view>

<!-- 加载提示或错误提示 -->
<view class="loading-container" wx:else>
  <text>加载中...</text>
</view>

<!-- Notice Dialog Component -->
<notice-dialog
  id="noticeDialog"
  show="{{ showNoticeDialog }}"
  title="入馆须知"
  notice="{{ ticketDetail.notice || '' }}"
  bind:confirm="onNoticeDialogConfirm">
</notice-dialog>
