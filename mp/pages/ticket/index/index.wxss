@import '/app.wxss';

.container {
  padding-top: 340rpx;
}

.container.no-sub-nav {
  padding-top: 275rpx;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 0 3rpx var(--shadow);
}

/* 自定义导航栏样式 */
.tickets-navbar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 190rpx;
  padding: 30rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid var(--shadow);
}

.navbar-city {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--dark);
}

.navbar-city-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.go-back {
  width: 32rpx;
  height: 32rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: black;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-tab-bar {
  display: flex;
  height: 80rpx;
  align-items: center;
  justify-content: space-evenly;
}

.nav-tab {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120rpx;
  height: 100%;
  font-size: 30rpx;
  color: var(--medium);
  position: relative;
  text-align: center;
}

.nav-tab.active {
  color: var(--dark);
  font-weight: bold;
}

.nav-tab.active::after {
  content: '';
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: var(--primary);
  border-radius: 3rpx;
}

.nav-theme-bar {
  display: flex;
  padding: 15rpx;
  border-bottom: 1rpx solid var(--silver);
  white-space: nowrap;
}

.nav-theme {
  display: inline-block;
  padding: 5rpx 20rpx;
  font-size: 24rpx;
  color: var(--dark);
  background-color: var(--silver);
  border-radius: 30rpx;
  margin-right: 20rpx;
  text-align: center;
}

.nav-theme.active {
  color: white;
  background-color: var(--primary);
}

/* 城市选择浮层样式 (基本不变) */
.city-picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
}

.city-picker-popup.active {
  transform: translateX(0);
}

.popup-header {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 30rpx;
  height: 190rpx;
  border-bottom: 1rpx solid var(--paper);
  box-sizing: border-box;
  background-color: white;
}

.popup-header .header-placeholder {
  width: 36rpx;
}

.popup-title {
  font-size: 34rpx;
  font-weight: bold;
  color: black;
  text-align: center;
  flex-grow: 1;
}

.search-bar-container {
  padding: 20rpx 30rpx;
  background-color: white;
  position: relative;
}

.search-input {
  height: 72rpx;
  line-height: 72rpx;
  background-color: var(--paper);
  border-radius: 36rpx;
  padding-left: 70rpx;
  padding-right: 30rpx;
  font-size: 28rpx;
  color: var(--dark);
  width: 100%;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: #aaa;
}

.search-bar-container .icon-search {
  position: absolute;
  left: 55rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #aaa;
}

.current-selection-section,
.all-cities-section {
  padding: 20rpx 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: var(--dark);
  margin-bottom: 20rpx;
  font-weight: bold;
}

.city-selected {
  display: inline-block;
  background-color: var(--paper);
  color: var(--dark);
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.city-selected.active {
  background-color: var(--primary);
  color: white;
}

.city-list-scroll {
  flex-grow: 1;
  height: 0;
  background-color: white;
  position: relative;
}

.city-letter-header {
  font-size: 28rpx;
  color: var(--dark);
  padding: 20rpx 0;
  background-color: white;
  font-weight: bold;
}

.city-name {
  font-size: 30rpx;
  color: var(--dark);
  padding: 25rpx 0;
  border-bottom: 1rpx solid var(--paper);
}

.city-name:last-child {
  border-bottom: none;
}

.alphabet-indexer {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  z-index: 1001;
  background-color: transparent;
}

.alphabet-letter {
  font-size: 22rpx;
  color: var(--primary);
  padding: 6rpx 8rpx;
  text-align: center;
}
