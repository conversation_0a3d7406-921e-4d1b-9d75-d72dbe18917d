<view class="container {{subNavBarThemes && subNavBarThemes.length <= 1 ? 'no-sub-nav' : ''}}">
  <view class="fixed-header">
    <!-- 自定义导航栏 -->
    <view class="tickets-navbar">
      <view class="navbar-city" bindtap="openCityPicker">
        <text>{{selectedCity === '全国' ? '全国' : selectedCity}}</text>
        <image class="navbar-city-icon" src="/assets/icons/down-solid.svg"></image>
      </view>
      <view class="navbar-title">展览列表</view>
    </view>

    <!-- 导航栏 -->
    <view class="nav-tab-bar">
      <view class="nav-tab {{activeNav === 'digital_exhibition' ? 'active' : ''}}"
            data-type="digital_exhibition"
            bindtap="onNavTap">数字展览</view>
      <block wx:if="{{ theaterEnable }}">
        <view class="nav-tab {{activeNav === 'space_theater' ? 'active' : ''}}" data-type="space_theater" bindtap="onNavTap">空间剧场</view>
      </block>
      <view class="nav-tab {{activeNav === 'joint_ticket' ? 'active' : ''}}" data-type="joint_ticket" bindtap="onNavTap">联票</view>
    </view>

    <!-- 子导航栏 (示例，根据实际需求调整) -->
    <scroll-view class="nav-theme-bar"
                 scroll-x="true"
                 wx:if="{{subNavBarThemes && subNavBarThemes.length > 0}}">
      <view wx:for="{{subNavBarThemes}}"
            wx:key="*this"
            class="nav-theme {{activeSubNavTheme === item ? 'active' : ''}}"
            data-theme="{{item}}"
            bindtap="onSubNavTap">
        {{item}}
      </view>
    </scroll-view>
  </view>

  <!-- 购票列表 -->
  <view wx:if="{{tickets.length > 0}}" class="item-list">
    <list-item
        wx:for="{{tickets}}"
        wx:key="id"
        thumbnail="{{item.thumbnail}}"
        title="{{item.title}}"
        descriptions="{{['地址: ' + item.addr]}}"
        price="{{item.prices[0]}}"
        data-id="{{item.id}}"
        bindtap="goToDetail"
    ></list-item>
  </view>
  <view wx:else class="empty">
    <image class="empty-image" src="/assets/empty.png" mode="widthFix"></image>
    <text>当前城市或分类下暂无展览</text>
  </view>

  <!-- 城市选择浮层 (初始隐藏) -->
  <view class="city-picker-popup {{showCityPicker ? 'active' : ''}}">
    <!-- 浮层头部 -->
    <view class="popup-header">
      <view class="back-icon" bindtap="closeCityPicker">
        <image class="go-back" src="/assets/icons/left-solid.svg"></image>
      </view>
      <view class="popup-title">选择城市</view>
      <view class="header-placeholder"></view>
    </view>

    <!-- 搜索框 -->
    <view class="search-bar-container">
      <input class="search-input" placeholder="请输入城市名称查询" bindinput="onSearchInput" value="{{searchText}}"/>
      <text class="iconfont icon-search">🔍</text>
    </view>

    <!-- 当前选择 -->
    <view class="current-selection-section">
      <view class="section-title">当前选择</view>
      <view class="city-selected active"
            bindtap="onCityTap"
            data-cityname="全部城市">
        {{selectedCity === '全国' ? '全国' : selectedCity}}
      </view>
    </view>

    <!-- 城市列表 -->
    <scroll-view scroll-y="true" class="city-list-scroll" scroll-into-view="{{scrollToLetter}}" scroll-with-animation="true">
      <view class="all-cities-section">
        <view class="section-title">城市列表</view>
        <view class="city-name" bindtap="onCityTap" data-cityname="全部城市">全国</view>
        <!-- 城市按字母分组渲染 -->
        <block wx:for="{{groupedCities}}" wx:for-item="group" wx:key="letter">
          <view id="letter-{{group.letter}}" class="city-group">
            <view class="city-letter-header">{{group.letter}}</view>
            <view class="city-name" wx:for="{{group.cities}}" wx:key="name" bindtap="onCityTap" data-cityname="{{item.name}}">
              {{item.name}}
            </view>
          </view>
        </block>
        <view wx:if="{{groupedCities.length === 0 && searchText}}" class="empty">
          未找到相关城市
        </view>
      </view>
    </scroll-view>

    <!-- 右侧字母索引 -->
    <view class="alphabet-indexer">
      <view class="alphabet-letter" wx:for="{{alphabet}}" wx:key="*this" bindtap="scrollToLetter" data-letter="{{item}}">
        {{item}}
      </view>
    </view>
  </view>
</view>
