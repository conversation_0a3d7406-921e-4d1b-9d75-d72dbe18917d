<view class="container">
  <navbar />

  <!-- 手机号输入对话框 -->
  <view class="phone-dialog {{ showPhoneDialog ? 'show' : '' }}">
    <view class="dialog-content">
      <view class="dialog-title">请先绑定手机</view>
      <input type="number"
             class="phone-input"
             maxlength="11"
             placeholder="请输入11位手机号"
             bindinput="onPhoneInput"
             value="{{ inputPhone }}" />
      <view class="dialog-desc">此手机号将作为观展时的验票凭证，请勿填错</view>
      <view class="dialog-buttons">
        <button class="btn cancel" bindtap="closePhoneDialog">取消</button>
        <button class="btn {{ isValidPhone ? '' : 'disabled' }}"
                bindtap="submitPhone"
                disabled="{{ !isValidPhone }}">确定</button>
      </view>
    </view>
  </view>

  <!-- 页面标题区域 -->
  <view class="main-banner" style="padding-top: {{ navBarBottom }}px;">
    <view class="section">
      <text class="ticket-title">{{ title }}</text>
    </view>
  </view>

  <!-- 主要内容区域，包含场次和票档选择 -->
  <view class="main-card">
    <!-- 场次选择 -->
    <view class="section">
      <text class="section-title">场次</text>
      <view class="options-list">
        <block wx:for="{{ timeslots }}" wx:key="*this">
          <view
            class="option-item {{ selectedTimeslot === item ? 'selected' : '' }}"
            data-timeslot="{{ item }}"
            bindtap="selectTimeslot"
          >
            <text class="option-text">{{ item }}</text>
          </view>
        </block>
      </view>
    </view>

    <!-- 票档选择 -->
    <block wx:if="{{ selectedTimeslot }}">
      <view class="section">
        <text class="section-title">票档</text>
        <view class="options-list grade-options-list">
          <block wx:for="{{ prices[selectedTimeslot] }}"
                 wx:for-item="priceInfo"
                 wx:for-index="gradeIndex"
                 wx:key="gradeIndex">
            <view class="option-item grade-option-item {{ selectedGrade === priceInfo[0] ? 'selected' : '' }}"
                  data-grade="{{ gradeIndex }}"
                  bindtap="selectGrade">
              <text class="option-text grade-name-text">{{ priceInfo[0] }}</text>
              <text class="option-text grade-price-text space-left">¥ {{ priceInfo[1] }}</text>
              <text wx:if="{{ priceInfo.length > 2 }}" class="grade-original-price-text space-left">
              （原价¥{{ priceInfo[2] }}）
              </text>
            </view>
          </block>
        </view>
      </view>
    </block>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <!-- 数量选择器 -->
    <view class="quantity-section-wrapper">
      <view class="quantity-text-info">
        <text class="quantity-label">数量</text>
        <text class="quantity-limit">每单限购 10 份</text>
      </view>
      <view class="quantity-controls">
        <view class="quantity-btn {{ quantity === 1 ? 'disabled' : '' }}" bindtap="decreaseQuantity">-</view>
        <text class="quantity-value">{{ quantity }}份</text>
        <view class="quantity-btn {{ quantity > 9 ? 'disabled' : '' }}" bindtap="increaseQuantity">+</view>
      </view>
    </view>

    <!-- 总价、确认按钮 -->
    <view class="bottom-action-row">
      <view class="price-summary">
        <text class="total-price-label">总价：</text>
        <text class="total-price-value">{{ amount }}</text>
        <text class="total-price-suffix">元</text>
      </view>
      <view class="purchase-button-wrapper">
        <block wx:if="{{ phone }}">
          <button class="purchase-button btn {{ buttonDisabled ? 'disabled' : '' }}"
                  bindtap="handlePurchase"
                  disabled="{{ buttonDisabled }}">
            {{ buttonText }}
          </button>
        </block>
        <block wx:else>
          <button class="purchase-button btn {{ buttonDisabled ? 'disabled' : '' }}"
                  open-type="getPhoneNumber"
                  bindgetphonenumber="getPhoneAndPurchase"
                  disabled="{{ buttonDisabled }}">
            {{ buttonText }}
          </button>
        </block>
      </view>
    </view>
  </view>
</view>
