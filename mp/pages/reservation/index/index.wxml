<view class="container">
  <!-- 预约列表 -->
  <view wx:if="{{reservations.length > 0}}" class="item-list">
    <list-item
        wx:for="{{reservations}}"
        wx:key="id"
        thumbnail="{{item.thumbnail}}"
        title="{{item.title}}"
        descriptions="{{['展期：' + item.start + ' 至 ' + item.end, '地点：' + item.city[0]]}}"
        buttonText="去预约"
        data-id="{{item.id}}"
        bind:buttontap="goToReservationChoice"
    ></list-item>
  </view>

  <view wx:else class="empty">
    <image class="empty-image" src="/assets/empty.png" mode="widthFix"></image>
    <text>暂无需要预约的展览</text>
  </view>
</view>
