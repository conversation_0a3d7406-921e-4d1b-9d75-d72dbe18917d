import api from '../../../utils/http';

Page({
  data: {
    statusBarHeight: 0, // 状态栏高度
    reservations: []
  },

  onLoad() {
    const res = wx.getWindowInfo();
    this.setData({
      statusBarHeight: res.statusBarHeight
    });
  },

  onShow() {
    // 更新 Docker 栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 3 });
    }

    // 获取预约票列表
    api.getReserveList()
      .then((res) => {
        this.setData({ reservations: res });
      });
  },

  goToReservationChoice(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/reservation/choice/choice?id=${id}`
    });
  }
});
