import api from '../../../utils/http';

Page({
  data: {
    selectedTicket: {}
  },

  onLoad(options) {
    // 从URL参数获取展览ID
    const ticketId = options.id;
    if (ticketId) {
      api.getTicketDetail(ticketId)
        .then((res) => {
          this.setData({ selectedTicket: res });
        });
    }
  },

  // 选择小程序购票预约
  selectMiniProgramReservation() {
    if (this.data.selectedTicket) {
      wx.navigateTo({
        url: `/pages/ticket/purchase/purchase?id=${this.data.selectedTicket.id}&type=reserve`
      });
    } else {
      wx.showToast({ title: '请先选择展览', icon: 'none' });
    }
  },

  // 选择其它渠道预约
  selectOtherChannelReservation() {
    if (this.data.selectedTicket) {
      wx.navigateTo({
        url: `/pages/reservation/reserve/reserve?id=${this.data.selectedTicket.id}`
      });
    } else {
      wx.showToast({
        title: '请先选择展览',
        icon: 'none'
      });
    }
  }
});
