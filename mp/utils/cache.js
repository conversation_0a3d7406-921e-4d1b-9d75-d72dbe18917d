class Cache {
  static prefix = '__Cache__';
  static _data = {};

  // 生成缓存 Key
  static makeKey(key) {
    return `${Cache.prefix}${key}`;
  }

  // 检查 Key
  static checkKey(key) {
    if (!key || typeof key !== 'string') {
      throw new Error('Cache key must be a non-empty string');
    }
  }

  // 设置缓存
  static set(key, value, expire = 0) {
    Cache.checkKey(key);
    expire = expire > 0 ? Date.now() + expire * 1000 : Infinity;
    Cache._data[Cache.makeKey(key)] = { value, expire };
  }

  // 获取缓存
  static get(key) {
    Cache.checkKey(key);
    const _key = key.startsWith(Cache.prefix) ? key : Cache.makeKey(key);
    const data = Cache._data[_key];
    if (!data) return null;

    if (!data.expire || Date.now() > data.expire) {
      Cache.del(_key);
      return null;
    }
    // console.log(`CacheHit: ${key}`)
    return data.value;
  }

  // 删除缓存
  static del(key) {
    Cache.checkKey(key);
    delete Cache._data[Cache.makeKey(key)];
  }

  // 所有的 Key
  static keys() {
    const result = [];
    const len = Cache.prefix.length;
    Object.keys(Cache._data).forEach((key) => {
      if (key.startsWith(Cache.prefix)) {
        result.push(key.slice(len));
      }
    });
    return result;
  }

  // 所有的 Value
  static values() {
    const result = [];
    Object.keys(Cache._data).forEach((key) => {
      if (key.startsWith(Cache.prefix)) {
        let value = Cache.get(key);
        if (value) {
          result.push(value);
        }
      }
    });
    return result;
  }

  // 清空缓存
  static clear() {
    Object.keys(Cache._data).forEach((key) => {
      if (key.startsWith(Cache.prefix)) {
        delete Cache._data[key];
      }
    });
  }
}

export default Cache;
