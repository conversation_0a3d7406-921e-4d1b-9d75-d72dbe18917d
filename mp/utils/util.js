const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : `0${n}`;
};

const isoTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return `${[year, month, day].map(formatNumber).join('-')} ${[hour, minute, second].map(formatNumber).join(':')}`;
};

const reloadCurrentPage = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const route = currentPage.route
  const options = currentPage.options
  const query = Object.keys(options).map(k => `${k}=${options[k]}`).join('&')
  const url = `/${route}?${query}`
  wx.redirectTo({ url })
}

export default {
  isoTime,
  reloadCurrentPage
};
