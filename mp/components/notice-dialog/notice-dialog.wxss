.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--translucence);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-container {
  min-width: 62vw;
  max-width: 80vw;
  padding: 50rpx;
  text-align: center;
  background-color: white;
  border-radius: 16rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 10rpx 0;
}

.content {
  font-size: 28rpx;
  color: var(--medium);
  margin-bottom: 50rpx;
  text-align: left;
  line-height: 1.8;
}

.btn {
  padding: 20rpx 40rpx;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  color: white;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.4;
  border: 1rpx solid transparent;
  border-radius: 100rpx;
  box-sizing: border-box;
}

.btn:active {
  background-color: var(--primary-deep);
}
