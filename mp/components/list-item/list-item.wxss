.item {
  display: flex;
  background-color: white;
  width: 720rpx;
  padding: 20rpx;
  margin: 0 auto 18rpx auto;
  align-items: flex-start;
  border-radius: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 0 10rpx var(--shadow);
}

.item-thumbnail {
  width: 180rpx;
  height: 240rpx;
  margin-right: 25rpx;
  border-radius: 16rpx;
}

.item-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  position: relative;
  min-height: 240rpx;
  justify-content: space-between;
}

.item-title {
  display: -webkit-box;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: var(--dark);
  line-height: 1.8;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-desc-list {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.item-desc {
  font-size: 24rpx;
  color: var(--medium);
  line-height: 1.6;
}

.item-footer {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  margin-top: auto;
}

.footer-wrapper {
  display: flex;
  align-items: baseline;
  color: var(--primary);
}

.price-symbol {
  font-size: 28rpx;
  margin-right: 4rpx;
}

.price-value {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 4rpx;
}

.price-suffix {
  font-size: 24rpx;
  color: var(--light);
}

.btn {
  width: 160rpx;
  max-width: 160rpx;
  margin: 0;
  padding: 12rpx;
  font-size: 0.9rem;
  line-height: 1.2;
  font-weight: 700;
  color: white;
  background-color: var(--primary);
  border-radius: 100rpx;
  box-sizing: border-box;
  box-shadow: 0 0 5rpx var(--shadow);
}
