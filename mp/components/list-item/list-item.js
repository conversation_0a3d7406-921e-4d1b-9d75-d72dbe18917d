Component({
  properties: {
    // 缩略图URL
    thumbnail: {
      type: String,
      value: ''
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 描述列表
    descriptions: {
      type: Array,
      value: []
    },
    // 价格
    price: {
      type: String,
      value: ''
    },
    // 价格后缀
    priceSuffix: {
      type: String,
      value: '起'
    },
    // 按钮文字
    buttonText: {
      type: String,
      value: ''
    }
  },

  methods: {
    // 按钮点击事件
    onButtonTap() {
      this.triggerEvent('buttontap');
    }
  }
});
