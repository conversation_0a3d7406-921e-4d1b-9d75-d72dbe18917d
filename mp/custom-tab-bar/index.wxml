<view class="tab-bar">
  <view wx:for="{{list}}"
        wx:key="index"
        class="tab-bar-item {{item.isSpecial ? 'special' : ''}}"
        data-path="{{item.pagePath}}"
        data-index="{{index}}"
        bindtap="switchTab">
    <image class="tab-bar-item-image {{item.isSpecial ? 'special-image' : ''}}"
           src="{{selected === index ? item.selectedIconPath : item.iconPath}}"></image>
    <view wx:if="{{item.text}}"
          class="tab-bar-item-text"
          style="color: {{ selected === index ? selectedColor : color }}">
      {{ item.text }}
    </view>
  </view>
</view>
