Component({
  data: {
    selected: 0,
    color: '#b7b7b7',
    selectedColor: '#c8ac60',
    list: [
      {
        pagePath: '/pages/home/<USER>/index',
        iconPath: '/assets/tabbar/home-regular.png',
        selectedIconPath: '/assets/tabbar/home-solid.png',
        text: '首页'
      },
      {
        pagePath: '/pages/ticket/index/index',
        iconPath: '/assets/tabbar/ticket-regular.png',
        selectedIconPath: '/assets/tabbar/ticket-solid.png',
        text: '购票'
      },
      {
        pagePath: '/pages/order/checkin/checkin',
        iconPath: '/assets/tabbar/qr-code.png',
        selectedIconPath: '/assets/tabbar/qr-code.png',
        text: null,
        isSpecial: true
      },
      {
        pagePath: '/pages/reservation/index/index',
        iconPath: '/assets/tabbar/check-regular.png',
        selectedIconPath: '/assets/tabbar/check-solid.png',
        text: '预约'
      },
      {
        pagePath: '/pages/user/index/index',
        iconPath: '/assets/tabbar/user-regular.png',
        selectedIconPath: '/assets/tabbar/user-solid.png',
        text: '我的'
      }
    ]
  },

  lifetimes: {
    attached() {
      this.updateSelected();
    }
  },

  methods: {
    updateSelected() {
      const pages = getCurrentPages();
      if (pages.length === 0) return;

      // 获取当前页面路径
      const currentPage = pages[pages.length - 1];
      const route = currentPage.route;

      let selected = 0;
      this.data.list.forEach((item, index) => {
        // 根据页面路径判断是否选中
        if (item.pagePath === `/${route}`) {
          selected = index;
        }
      });

      // 更新选中状态
      this.setData({ selected });
    },

    switchTab(e) {
      const data = e.currentTarget.dataset;
      const index = parseInt(data.index);
      const path = data.path;

      // 更新选中状态
      this.setData({ selected: index });

      // 跳转页面
      wx.switchTab({
        url: path,
        fail: (err) => {
          console.error('跳转失败：', err);
        }
      });
    }
  }
});
