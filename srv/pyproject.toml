[project]
name = "srv"
version = "0.1.0"
description = "华夏漫游小程序服务端"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aerich>=0.9.0",
    "aiofiles>=24.1.0",
    "asyncpg>=0.30.0",
    "fastapi[all]>=0.115.12",
    "filetype>=1.2.0",
    "httpx>=0.28.1",
    "jinja2>=3.1.6",
    "pycryptodome>=3.23.0",
    "pypinyin>=0.54.0",
    "python-jose[pycryptodome]>=3.5.0",
    "redis>=6.2.0",
    "tomlkit>=0.13.3",
    "tortoise-orm>=0.25.1",
    "wechatpayv3>=1.3.11",
    "xxhash>=3.5.0",
]

[dependency-groups]
dev = [
    "djlint>=1.36.4",
    "ipython>=9.3.0",
    "mypy>=1.16.0",
    "pgcli>=4.3.0",
    "types-aiofiles>=24.1.0.20250606",
]

[tool.ruff]
line-length = 120

[tool.ruff.format]
quote-style = "single"

[tool.aerich]
tortoise_orm = "config.server.DATABASE"
location = "./migrations"
src_folder = "./."
