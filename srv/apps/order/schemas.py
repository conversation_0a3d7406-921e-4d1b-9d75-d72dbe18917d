from datetime import datetime
from enum import StrEnum

from pydantic import BaseModel, Field, field_validator
from tortoise.expressions import Q

from apps.exhi.schemas import TicketCategory as Catg


class OrderStatus(StrEnum):
    """订单状态"""

    pending = '待支付'
    failed = '支付失败'
    paid = '待使用'  # 支付成功，但未核销
    used = '已使用'
    canceled = '已取消'
    closed = '已关闭'
    refunding = '退款中'
    refunded = '已退款'


class OrderFilter(StrEnum):
    """订单过滤"""

    paid = '待使用'
    used = '已使用'
    refund = '退款'
    unpaid = '未支付'

    @property
    def query(self) -> Q:
        match self:
            case self.paid:
                return Q(status=OrderStatus.paid)
            case self.used:
                return Q(status=OrderStatus.used)
            case self.refund:
                return Q(status__in=[OrderStatus.refunding, OrderStatus.refunded])
            case self.unpaid:
                return Q(
                    status__in=[
                        OrderStatus.pending,
                        OrderStatus.failed,
                        OrderStatus.canceled,
                        OrderStatus.closed,
                    ]
                )
            case _:
                raise ValueError(f'未知的订单状态: {self}')


class OrderSubmitForm(BaseModel):
    """订单创建请求"""

    tid: int = Field(..., description='展票ID')
    catg: str = Field(..., description='票种类型')
    quantity: int = Field(..., ge=1, le=10, description='购买数量')
    amount: float = Field(..., gt=0, description='订单价格')
    timeslot: str = Field(..., description='场次')
    grade: str = Field(..., description='票档')

    @field_validator('catg')
    @classmethod
    def validate_ticket_catg(cls, v):
        if v not in Catg:
            raise ValueError('票种类型无效')
        return v


class PaymentResp(BaseModel):
    """支付响应"""

    order_id: int
    trade_no: str
    prepay_id: str
    pay_params: dict
    expire_time: datetime


class CheckinForm(BaseModel):
    """核销请求"""

    vcode: str = Field(..., max_length=64, description='订单ID')


class OidForm(BaseModel):
    order_id: int = Field(..., description='订单ID')
