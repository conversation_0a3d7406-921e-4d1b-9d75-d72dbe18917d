import re
import time

from fastapi import APIRouter
from jose import jwt
from tortoise.exceptions import IntegrityError

from apps.user.models import User
from apps.user.schemas import BindPhoneForm
from config import JWT_ALGORITHM, SECRET_KEY, SESSION_MAX_AGE
from libs import wxmp
from libs.cache import redis
from libs.http import abort
from libs.state import State

router = APIRouter()


@router.get('/weixin/login')
async def weixin_login(js_code: str):
    """微信登录"""
    wx_session = await wxmp.login(js_code)

    # 正常的微信 Session 包含 session_key, openid
    # 若小程序已绑定微信开放平台帐号则会有 unionid
    if wx_session.openid and wx_session.session_key:
        user_dict = {'openid': wx_session.openid, 'name': '微信用户'}
        if wx_session.unionid:
            user_dict['union_id'] = wx_session.unionid
            try:
                user, _ = await User.get_or_create(union_id=wx_session.unionid, defaults=user_dict)
            except IntegrityError:
                user, _ = await User.update_or_create(union_id=wx_session.unionid, defaults=user_dict)
        else:
            user, _ = await User.get_or_create(openid=wx_session.openid, defaults=user_dict)

        # 后端 Redis 中保存 wx_session
        await redis.set(f'WxSession::{user.id}', wx_session, SESSION_MAX_AGE)

        # 前端的 Authorization Token 中保存 uid
        now = int(time.time())
        jwt_token = jwt.encode({'uid': user.id, 'iat': now, 'exp': now + SESSION_MAX_AGE}, SECRET_KEY, JWT_ALGORITHM)

        return {
            'jwt': jwt_token,
            'openid': wx_session.openid,
            'name': user.name,
            'avatar': user.avatar,
            'phone': user.phone,
            'isAdm': user.is_adm,
        }

    return abort(401, wx_session.errmsg or '登录失败')


@router.get('/weixin/profile')
async def weixin_profile(encrypted: str, iv: str):
    """获取微信用户信息，并解密"""
    user: User = State.get('user')
    if wx_session := await redis.get(f'WxSession::{user.id}'):
        try:
            # 微信默认头像特征码
            fingerprint = 'POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg'
            profile = wxmp.decrypt_data(encrypted, iv, wx_session.session_key)
            if profile.nickName != '微信用户' and fingerprint not in (profile.avatarUrl or ''):
                user.name, user.avatar = profile.nickName, profile.avatarUrl  # type: ignore
                await user.save()
            return user.to_dict(exclude=['id'])
        except (ValueError, KeyError) as err:
            return abort(401, err)
    return abort(401, '登录状态异常')


@router.get('/get/phone')
async def get_phone(encrypted: str, iv: str, code: str):
    user: User = State.get('user')
    if encrypted and iv:
        if wx_session := await redis.get(f'WxSession::{user.id}'):
            phone_info = wxmp.decrypt_data(encrypted, iv, wx_session.session_key)
            if phone_info.purePhoneNumber:
                try:
                    user.phone = phone_info.purePhoneNumber
                    await user.save()
                    return {'phone': phone_info.purePhoneNumber}
                except IntegrityError:
                    return abort(409, '此手机号已绑定其他账号')

    token = await wxmp.get_access_token()
    if not token.access_token:
        return abort(401, token.errmsg or '获取 AccessToken 失败')

    phone_info = await wxmp.get_phone_num(code, token.access_token)
    if phone_info.purePhoneNumber:
        user.phone = phone_info.purePhoneNumber
        await user.save()
        return {'phone': phone_info.purePhoneNumber}
    else:
        return abort(401, phone_info.errmsg or '获取手机信息失败')


@router.post('/bind/phone')
async def bind_phone(form: BindPhoneForm):
    """绑定手机号"""
    user: User = State.get('user')

    # Validate phone number using regex
    if not re.match(r'^1\d{10}$', form.phone):
        return abort(400, '手机号格式不正确')

    try:
        user.phone = form.phone
        await user.save()
        return {'msg': 'OK'}
    except IntegrityError:
        return abort(500, '手机号已被绑定')
