from typing import ClassVar

from starlette.authentication import BaseUser
from tortoise import fields

from config import PAGE_SIZE
from libs.orm import Model


class User(Model, BaseUser):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=64, description='用户名')
    avatar = fields.CharField(max_length=256, null=True, description='头像')
    openid = fields.CharField(max_length=64, unique=True, description='微信 Open ID')
    union_id = fields.CharField(max_length=64, unique=True, null=True, description='微信的 Union ID')
    phone = fields.CharField(max_length=11, unique=True, null=True, description='手机号')
    is_adm = fields.BooleanField(default=False, description='是否是管理员')

    # 用户状态
    created = fields.DatetimeField(auto_now_add=True, description='创建时间')

    class Meta:  # type: ignore
        table = 'users'
        ordering: ClassVar[list[str]] = ['-id']

    @property
    def is_authenticated(self):
        return True

    @property
    def display_name(self):
        return self.name

    @property
    def identity(self):
        return self.openid

    def orders(self, status: str | None = None, limit: int = PAGE_SIZE, offset: int = 0):
        """获取用户订单列表"""
        from apps.order.models import Order

        kwargs = {'uid': self.id, 'status': status} if status else {'uid': self.id}
        query = Order.filter(**kwargs)

        return query.order_by('-created').limit(limit).offset(offset)
