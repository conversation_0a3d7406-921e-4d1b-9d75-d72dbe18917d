# 华夏漫游小程序服务端

本项目是“华夏漫游”微信小程序的服务端，主要为小程序提供 RESTful API 接口，以及为管理员提供管理后台。

## 技术选型

- 操作系统: Ubuntu 24.04 及以上版本
- 编程语言: Python 3.12 及以上版本
- Web 框架: FastAPI
- 模板引擎: Jinja2
- ORM: Tortoise ORM
- 数据库: PostgreSQL v17.5
- 缓存: Redis v7.22
- 反向代理: Nginx v1.28

## RESTful API

### 基础数据

基础数据是项目中会多次使用的一些固定格式的数据。下文的接口中如果出现被尖括号包着的数据结构则表明这里是一处基础数据。
比如，接口返回值中如果出现 `<ticket>` 则表明此处是一个 ticket 数据。

1. 门票 ticket:

    ```json
        {
            "id": 1,
            "title": "【贵阳】丛林探秘",
            "addr": "贵阳·壹号购物中心",
            "themes": ["重返侏罗纪"],
            "price": [68, 168],
            "notice": "入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程。",
            "thumbnail": "https://mp.seamile.cn/static/mp/a.jpg",
            "banner": "https://mp.seamile.cn/static/mp/a.jpg",
            "detail": [
                "https://mp.seamile.cn/static/mp/x-1.jpg",
                "https://mp.seamile.cn/static/mp/x-2.jpg",
                "https://mp.seamile.cn/static/mp/x-3.jpg",
                "https://mp.seamile.cn/static/mp/x-4.jpg",
                "https://mp.seamile.cn/static/mp/x-5.jpg",
                "https://mp.seamile.cn/static/mp/x-6.jpg",
                "https://mp.seamile.cn/static/mp/x-7.jpg"
            ]
        }
    ```

2. 用户 user:

    ```json
    {}
    ```

## 接口列表

1. 获取首页轮播图及热展列表
    - API: `/api/home`
    - method: GET
    - arguments: None
    - returns:

        ```json
        {
            "slides": [
                "https://mp.seamile.cn/static/mp/a.jpg",
                "https://mp.seamile.cn/static/mp/b.jpg"
            ],
            "exhi_rcmd": [
                <ticket>,
                <ticket>,
                <ticket>
            ]
        }
        ```

2. 获取数字展览列表
    - API: `/api/ticket/digital`
    - method: GET
    - arguments:
        - pg: int, 页码, 默认为 1，下拉时自增 1
    - returns:

        ```json
        [
            <ticket>,
            <ticket>,
            <ticket>
        ]
        ```

3. 获取空间剧场列表
    - API: `/api/ticket/theater`
    - method: GET
    - arguments:
        - pg: int, 页码, 默认为 1，下拉时自增 1
    - returns:

        ```json
        [
            <ticket>,
            <ticket>,
            <ticket>
        ]
        ```

4. 获取联票列表
    - API: `/api/ticket/joint`
    - method: GET
    - arguments:
        - pg: int, 页码, 默认为 1，下拉时自增 1
    - returns:

        ```json
        [
            <ticket>,
            <ticket>,
            <ticket>
        ]
        ```

5. 获取门票详情
    - API: `/api/ticket/detail`
    - method: GET
    - arguments:
        - id: int
    - returns:

        ```json
        <ticket>
        ```

6. 获取票价信息
    - API: `/api/ticket/prices`
    - method: GET
    - arguments:
        - id: int
    - returns:

        ```json
        {
            "全通票": {
                "单人": {"cur": 50, "ori": 555},
                "双人": {"cur": 80, "ori": 666},
                "三人": {"cur": 120, "ori": 777},
                "儿童": {"cur": 30},
                "亲子": {"cur": 70},
                "家庭": {"cur": 10}
            },
            "平日票": {
                "单人": {"cur": 50, "ori": 555},
                "双人": {"cur": 80, "ori": 666},
                "三人": {"cur": 120, "ori": 777},
                "儿童": {"cur": 30},
                "亲子": {"cur": 70},
                "家庭": {"cur": 10}
            }
        }
        ```

7. 下单
    - API: `/api/order/checkout`
    - method: GET
    - arguments:
        - id: int
    - returns:

        ```json
        {
            "order_id": 123321,
            "status": 0
        }
        ```

8. 支付
    - API: `/api/order/payment`
    - method: GET
    - arguments:
        - id: int, 订单 ID
    - returns:

        ```json
        {
            "status": 0
        }
        ```

9. 预约
    - API: `/api/order/reserve`
    - method: GET
    - arguments:
        - id: int
    - returns:

        ```json
        {}
        ```

10. 核销订单
    - API: `/api/order/verification`
    - method: POST
    - arguments:
        - id: int
    - returns:

        ```json
        {}
        ```

11. 获取订单列表
    - API: `/api/order/list`
    - method: GET
    - arguments: None
    - returns:

        ```json
        {}
        ```

12. 获取预约列表
    - API: `/api/reservation/list`
    - method: GET
    - arguments: None
    - returns:

        ```json
        {}
        ```

13. 登录
    - API: `/api/user/login`
    - method: POST
    - arguments: None
    - returns:

        ```json
        {}
        ```

14. 退出
    - API: `/api/user/logout`
    - method: POST
    - arguments: None
    - returns:

        ```json
        {}
        ```

15. 获取个人信息
    - API: `/api/user/info`
    - method: GET
    - arguments: None
    - returns:

        ```json
        {}
        ```
