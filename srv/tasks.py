import asyncio
from contextlib import asynccontextmanager

from fastapi import FastAPI

from apps.order.models import Order


async def close_expire_orders():
    """关闭过期订单"""
    while True:
        await Order.close_all_expired_orders()
        await asyncio.sleep(1)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    task = asyncio.create_task(close_expire_orders())

    yield  # 应用运行期间保持运行

    # 关闭前执行
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        pass
