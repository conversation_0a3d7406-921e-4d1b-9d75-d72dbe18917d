"""FastAPI Configs"""

import os
from multiprocessing import cpu_count
from zoneinfo import ZoneInfo

APP_NAME = 'HuaXiaMP'
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DEBUG = True if os.getenv('HXENV') == 'test' else False
DOMAIN = 'mp.seamile.cn' if not DEBUG else 'localhost:8000'
ALLOWED_HOSTS = ['mp.seamile.cn', 'localhost', '127.0.0.1']  # 允许的主机列表
TRUSTED_HOSTS = ['mp.seamile.cn', 'localhost', '127.0.0.1']  # 信任的主机列表

# 设置全局时区
os.environ['TZ'] = 'Asia/Shanghai'
TZ = TIME_ZONE = ZoneInfo(os.environ['TZ'])

# 静态文件配置
STATIC_URL = '/static'
STATIC_DIR = os.path.join(BASE_DIR, 'static')
# 上传文件设置
UPLOAD_URL = os.path.join(STATIC_URL, 'upload')
UPLOAD_DIR = os.path.join(STATIC_DIR, 'upload')
MAX_SIZE = 5 * 1024**2  # 5MB
ALLOWED_TYPES = ['jpg', 'jpeg', 'png']

UVICORN = {
    'reload': DEBUG,
    'workers': 1 if DEBUG else cpu_count() * 2,
    'access_log': DEBUG,  # 访问日志开关
    'use_colors': True,
    'backlog': 2048,
    'limit_concurrency': 10000,
    'limit_max_requests': 65535,
    'timeout_keep_alive': 65,  # 长连接超时时间
    'timeout_graceful_shutdown': 30.0,  # 强制关闭非空闲连接超时时间
}

SECRET_KEY = b'Zb\xeb/\xc9T\xb3\\j\x88O\xb7Qu\xa6\xbf\xbcT!D>dja/\xcd\x04d\x1fZ\xec2'
JWT_ALGORITHM = 'HS256'
SESSION_MAX_AGE = 86400  # 会话有效期 1 天
CACHE_MAX_AGE = 86400 * 7  # 缓存有效期 7 天

# Redis Config
REDIS = {
    'host': 'localhost',
    'port': 6379,
    'db': 13,  # Redis DB 选择
}

# Database Config
DATABASE = {
    'connections': {
        'default': 'postgres://seamile:pa9Bg<DNki5G8=7R@localhost:5432/huaxdb',
    },
    'apps': {
        APP_NAME: {
            'default_connection': 'default',
            'models': [
                'aerich.models',
                'admin.models',
                'apps.exhi.models',
                'apps.order.models',
                'apps.user.models',
            ],
        }
    },
}

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'simple': {
            'format': '%(asctime)s %(module)s.%(funcName)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'verbose': {
            'format': ('%(asctime)s %(levelname)s %(module)s.%(funcName)s (%(lineno)d): %(message)s'),
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'info': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': f'{BASE_DIR}/logs/info.log',
            'when': 'midnight',
            'backupCount': 30,
            'formatter': 'simple',
        },
        'error': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': f'{BASE_DIR}/logs/error.log',
            'when': 'W0',
            'backupCount': 4,
            'formatter': 'verbose',
        },
        'payment': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': f'{BASE_DIR}/logs/payment.log',
            'when': 'midnight',
            'backupCount': 30,
            'formatter': 'simple',
        },
    },
    'loggers': {
        'info': {
            'handlers': ['console' if DEBUG else 'info'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': True,
        },
        'error': {
            'handlers': ['console' if DEBUG else 'error'],
            'level': 'DEBUG' if DEBUG else 'WARNING',
            'propagate': True,
        },
        'payment': {
            'handlers': ['console' if DEBUG else 'payment'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': True,
        },
    },
}
