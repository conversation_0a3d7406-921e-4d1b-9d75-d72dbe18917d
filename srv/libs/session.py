from collections import UserDict
from typing import Any, Self
from uuid import uuid4

from fastapi import Request, Response

from config import DEBUG, DOMAIN, SECRET_KEY, SESSION_MAX_AGE
from libs.cache import redis
from libs.middleware import BaseMiddleware


class Session(UserDict):
    """会话管理器"""

    PREFIX = 'Session::'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._changed = False
        self._saved = False

    def __setitem__(self, key: str, value: Any) -> None:
        if key == '_id' and not value:
            raise ValueError('Session ID 不能为空')
        if key == 'uid' and not (isinstance(value, int) and value > 0):
            raise ValueError('User ID 必须为非 0 整数')

        super().__setitem__(key, value)
        self._changed = True
        self._saved = False

    def __delitem__(self, key: str) -> None:
        super().__delitem__(key)
        self._changed = True
        self._saved = False

    def clear(self) -> None:
        super().clear()
        self._changed = True
        self._saved = False

    @property
    def id(self) -> str | None:
        return self.get('_id')

    @property
    def cache_key(self) -> str:
        if not self.id:
            raise ValueError('Session ID 不能为空')
        return f'{self.PREFIX}{self.id}'

    @property
    def uid(self) -> int | None:
        """用户 ID"""
        return self.get('uid')

    @property
    def user_data(self):
        """返回会话数据"""
        return {k: v for k, v in self.items() if not k.startswith('_')}

    @property
    def is_empty(self) -> bool:
        """检查会话是否为空"""
        return not self.user_data

    @property
    def changed(self) -> bool:
        return self._changed

    @property
    def saved(self) -> bool:
        return self._saved

    @property
    def need_save(self) -> bool:
        """检查是否需要保存会话"""
        return self.changed and not self.saved

    @classmethod
    def new_id(cls) -> str:
        """生成新的 Session ID"""
        return bytes(a ^ b for a, b in zip(uuid4().bytes, SECRET_KEY, strict=False)).hex()

    async def save(self) -> str | None:
        """保存会话"""
        if self.is_empty:
            await self.delete()
            return None
        if not self.need_save:
            return None

        # 修改状态
        self._changed, self._saved = False, True

        if self.id:
            await redis.set(self.cache_key, self.user_data, SESSION_MAX_AGE)
            return None
        else:
            self['_id'] = self.new_id()
            await redis.set(self.cache_key, self.user_data, SESSION_MAX_AGE)
            return self.id

    @classmethod
    async def load(cls, session_id: str | None) -> Self:
        """加载会话"""
        if session_id:
            if user_data := await redis.get(f'{cls.PREFIX}{session_id}'):
                session = cls(user_data)
                session['_id'] = session_id
                return session
        return cls()

    def update(self, other: dict[str, Any]) -> None:  # type: ignore
        """更新会话数据"""
        for key, value in other.items():
            if not key.startswith('_'):
                self[key] = value

    async def delete(self) -> None:
        """删除会话"""
        if self.id:
            await redis.delete(self.cache_key)
        self.clear()


class SessionMiddleware(BaseMiddleware):
    """会话中间件"""

    path = '/adm'

    async def process_request(self, request: Request):
        """处理请求"""
        if request.url.path.startswith(self.path):
            sid = request.cookies.get('sid')  # 获取session id
            request.scope['session'] = await Session.load(sid)
        else:
            request.scope['session'] = None

    async def process_response(self, request: Request, response: Response) -> Response:  # type: ignore
        session = request.scope['session']  # type: ignore
        if isinstance(session, Session):
            if sid := await session.save():
                response.set_cookie(
                    key='sid',
                    value=sid,
                    max_age=SESSION_MAX_AGE,
                    path=self.path,
                    domain=None if DEBUG else f'.{DOMAIN}',
                    secure=not DEBUG,
                    httponly=True,
                    samesite='lax',
                )
            elif session.is_empty and 'sid' in request.cookies:
                response.delete_cookie(
                    'sid',
                    path=self.path,
                    domain=None if DEBUG else f'.{DOMAIN}',
                    samesite='lax',
                )  # 删除 Cookie 中的 sid
            return response
