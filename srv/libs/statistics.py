import datetime
import inspect
from collections.abc import Callable
from functools import wraps

from config import TZ
from libs.cache import redis


async def record_hit(identity: str, key: str, *ext_keys: str, score=1, expire: int = 0):
    """记录指定统计项的访问次数"""
    key = key.title().replace(' ', '')
    if ext_keys:
        params_key = ''.join(ek.title().replace(' ', '') for ek in ext_keys)
        rank_key = f'Hit::{key}:{params_key}'
    else:
        rank_key = f'Hit::{key}'

    await redis.zincrby(rank_key, score, identity)

    if expire > 0:
        await redis.expire(rank_key, expire)


def record_hit_by(name: str, key: str, *ext_keys: str, score=1, expire: int = 0):
    """
    页面访问统计装饰器

    Args:
        @name: 记录对象的参数名，如 'uid'、'phone' 等
        @key: 统计项的名称，如 'PageView'、'APIRequest' 等
        @ext_keys: 统计项的扩展参数，如 'APIName'、'HTTPMethod' 等
        @score: 每次访问的计数，默认为 1
    """

    def decorator(func: Callable) -> Callable:
        st_key = key or func.__name__.title().replace('_', '')

        @wraps(func)
        async def wrapper(request, *args, **kwargs):
            # 从函数对象中获取所需参数
            params = inspect.signature(func).bind(request, *args, **kwargs)
            params.apply_defaults()

            # 通过 Redis 的 ZSET 类型来记录访问次数
            if identity := params.arguments.get(name):
                await record_hit(identity, st_key, *ext_keys, score=score)

            return await func(request, *args, **kwargs)

        return wrapper

    return decorator


def record_monthly(key: str, *ext_keys: str, score=1, expire: int = 86400 * 365 * 2):
    """记录月访问次数 (默认保留 2 年)"""
    now = datetime.datetime.now(TZ)
    month = now.strftime('%Y-%m')
    return record_hit_by(month, key, *ext_keys, score=score, expire=expire)


def record_weekly(key: str, *ext_keys: str, score=1, expire: int = 86400 * 365):
    """记录周访问次数 (默认保留 1 年)"""
    now = datetime.datetime.now(TZ)
    monday = now - datetime.timedelta(days=now.weekday())
    week = monday.strftime('%Y-%WW')
    return record_hit_by(week, key, *ext_keys, score=score, expire=expire)


def record_daily(key: str, *ext_keys: str, score=1, expire: int = 86400 * 30):
    """记录日访问次数 (默认保留 1 个月)"""
    now = datetime.datetime.now(TZ)
    day = now.strftime('%Y-%m-%d')
    return record_hit_by(day, key, *ext_keys, score=score, expire=expire)


def record_hourly(key: str, *ext_keys: str, score=1, expire: int = 86400 * 3):
    """记录小时访问次数 (默认保留 3 天)"""
    now = datetime.datetime.now(TZ)
    hour = now.strftime('%Y-%m-%d_%H')
    return record_hit_by(hour, key, *ext_keys, score=score, expire=expire)
