import json
from collections import UserDict
from copy import copy
from typing import Any, Self


class AttrDict(UserDict):
    def __init__(self, initial: str | dict | list | None = None, strict=False):
        self._strict = strict
        if isinstance(initial, dict):
            object.__setattr__(self, 'data', initial)
        elif isinstance(initial, str):
            object.__setattr__(self, 'data', json.loads(initial))
        else:
            object.__setattr__(self, 'data', dict(initial or {}))

    def __getattr__(self, key) -> Any:
        try:
            return object.__getattribute__(self, 'data')[key]
        except KeyError as err:
            if self._strict:
                raise AttributeError(f'{key!r} not found in MyDict') from err

    def __setattr__(self, key, value):
        if key.startswith('_'):
            object.__setattr__(self, key, value)
        else:
            raise AttributeError('MyDict is read-only')

    def __setitem__(self, key, value):
        raise TypeError('MyDict is read-only')

    def __delitem__(self, key):
        raise TypeError('MyDict is read-only')

    def copy(self) -> Self:
        return copy(self)

    def set(self, key, value):
        self.data[key] = value
