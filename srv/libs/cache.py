import logging
from collections.abc import Awaitable
from functools import wraps
from pickle import HIGHEST_PROTOCOL, UnpicklingError, dumps, loads  # noqa: S403
from typing import Any

from redis.asyncio import Redis as AioRedis
from redis.typing import AbsExpiryT, EncodableT, ExpiryT, KeysT, KeyT, PatternT

REDIS = {
    'host': 'localhost',
    'port': 6379,
    'db': 13,  # Redis DB 选择
}

__all__ = ['Redis', 'redis']


class Redis(AioRedis):
    """Redis client with pickle support"""

    def _serialize(self, value) -> bytes:
        """将值序列化为二进制格式"""
        return dumps(value, HIGHEST_PROTOCOL)

    def _deserialize(self, value, default: Any = None) -> Any:
        """将二进制值反序列化，失败时返回默认值或原始值"""
        if value is None:
            return default
        try:
            return loads(value)  # noqa: S301
        except UnpicklingError as e:
            logging.warning(f'Failed to unpickle value: {e}')
            return value

    def set(
        self,
        name: KeyT,
        value: Any,
        ex: ExpiryT | None = None,
        px: ExpiryT | None = None,
        nx: bool = False,
        xx: bool = False,
        keepttl: bool = False,
        get: bool = False,
        exat: AbsExpiryT | None = None,
        pxat: AbsExpiryT | None = None,
    ):
        """Set the value at key ``name`` to ``value``

        ``ex`` sets an expire flag on key ``name`` for ``ex`` seconds.
        ``px`` sets an expire flag on key ``name`` for ``px`` milliseconds.
        ``nx`` if set to True, set the value at key ``name`` to ``value`` only
            if it does not exist.
        ``xx`` if set to True, set the value at key ``name`` to ``value`` only
            if it already exists.
        ``keepttl`` if True, retain the time to live associated with the key.
            (Available since Redis 6.0)
        ``get`` if True, set the value at key ``name`` to ``value`` and return
            the old value stored at key, or None if the key did not exist.
            (Available since Redis 6.2)
        ``exat`` sets an expire flag on key ``name`` for ``ex`` seconds,
            specified in unix time.
        ``pxat`` sets an expire flag on key ``name`` for ``ex`` milliseconds,
            specified in unix time.
        """
        v_pickled = self._serialize(value)  # 将需要保存的值序列化处理
        return super().set(name, v_pickled, ex, px, nx, xx, keepttl, get, exat, pxat)

    async def get(self, name: KeyT, default: Any = None):
        """Return the value at key ``name``, or ``default`` if the key doesn't exist"""
        value = await super().get(name)
        return self._deserialize(value, default)

    def mset(self, mapping: dict[KeyT, Any]):  # type: ignore
        """
        Sets key/values based on a mapping. Mapping is a dictionary of
        key/value pairs. Both keys and values should be strings or types that
        can be cast to a string via str().
        """
        for k, v in mapping.items():
            mapping[k] = self._serialize(v)
        return super().mset(mapping)  # type: ignore

    async def mget(self, keys: KeysT, *args: EncodableT):
        """Returns a list of values ordered identically to ``keys``"""
        values = await super().mget(keys, *args)
        for idx, value in enumerate(values):
            values[idx] = self._deserialize(value)
        return values

    async def keys(self, pattern: PatternT = '*', **kwargs) -> list[str]:
        """Returns a list of keys matching ``pattern``"""
        result = [k.decode('utf-8') for k in await super().keys(pattern, **kwargs)]
        result.sort()
        return result

    def hset(  # type: ignore
        self,
        name: str,
        key: str | None = None,
        value: Any = None,
        mapping: dict | None = None,
        items: list | None = None,
    ) -> Awaitable[int]:
        """
        Set ``key`` to ``value`` within hash ``name``,
        ``mapping`` accepts a dict of key/value pairs that will be
        added to hash ``name``.
        ``items`` accepts a list of key/value pairs that will be
        added to hash ``name``.
        Returns the number of fields that were added.
        """
        if value is not None:
            value = self._serialize(value)
        if mapping is not None:
            mapping = {k: self._serialize(v) for k, v in mapping.items()}
        if items is not None:
            items = [v if i % 2 == 0 else self._serialize(v) for i, v in enumerate(items)]

        return super().hset(name, key, value, mapping, items)  # type: ignore

    async def hget(self, name: str, key: str, default: Any = None):
        """Return the value of ``key`` within the hash ``name``"""
        value = await super().hget(name, key)  # type: ignore
        return self._deserialize(value, default)

    async def hgetall(self, name: KeyT) -> dict[str, Any]:
        """Return a Python dict of the hash's name/value pairs"""
        items = {}
        for k, v in (await super().hgetall(name)).items():  # type: ignore
            k = k.decode('utf-8')
            items[k] = self._deserialize(v)
        return items

    async def get_pattern(self, pattern: str):
        """根据给定的模式获取匹配的键值对。"""
        keys = await self.keys(pattern)
        return dict(zip(keys, await self.mget(keys), strict=False))

    async def del_pattern(self, pattern: str):
        """异步删除匹配指定模式的键。"""
        if keys := await self.keys(pattern):
            return await self.delete(*keys)
        else:
            return 0


redis = Redis(**REDIS)  # type: ignore


class SkipCache(Exception):  # noqa: N818
    """跳过缓存的异常"""

    def __init__(self, value):
        self.value = value
        super().__init__()


def cache(key: str | None = None, expire: int | None = None):
    """缓存装饰器"""

    def gen_key(func, *args, **kwargs) -> str:
        """拼接参数"""
        arg_names = func.__code__.co_varnames[: func.__code__.co_argcount]
        kwargs.update(dict(zip(arg_names, args, strict=False)))
        s_kwargs = '&'.join(f'{k}={v}' for k, v in sorted(kwargs.items()))
        return f'Func::{s_kwargs}'

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            nonlocal key, expire
            if key is None:
                key = gen_key(func, *args, **kwargs)

            value = await redis.get(key)
            if value is not None:
                return value

            try:
                value = await func(*args, **kwargs)
                await redis.set(key, value, expire)
                return value
            except SkipCache as e:
                return e.value

        return wrapper

    return decorator
