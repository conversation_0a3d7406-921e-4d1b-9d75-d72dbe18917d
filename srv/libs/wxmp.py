import base64
import json
import logging

import httpx
from Crypto.Cipher import AES  # noqa: S413
from Crypto.Util.Padding import unpad
from wechatpayv3 import WeChatPay
from wechatpayv3.type import SignType

from config.payment import MP_APP_ID, MP_APP_SECRET, WX_PAYMENT
from libs.attrdict import AttrDict
from libs.cache import SkipCache, cache

logger = logging.getLogger('payment')


async def login(js_code: str):
    """
    微信登录

    :@param js_code: 小程序登录时获取的 code
    :@return: {
        "session_key: string,  # 会话密钥
        "unionid: string,      # 用户在开放平台的唯一标识符，若当前小程序已绑定到微信开放平台帐号下会返回，详见 UnionID 机制说明。
        "openid: string,       # 用户唯一标识
        "errmsg: string,       # 错误信息，请求失败时返回
        "errcode: int32,       # 错误码，请求失败时返回
    }
    """
    api = 'https://api.weixin.qq.com/sns/jscode2session'
    params = {
        'appid': MP_APP_ID,
        'secret': MP_APP_SECRET,
        'js_code': js_code,
        'grant_type': 'authorization_code',
    }
    async with httpx.AsyncClient() as cli:
        response = await cli.get(api, params=params)
        if response.status_code == 200:
            return AttrDict(response.json())
        return AttrDict({'errcode': -1, 'errmsg': '系统繁忙，请稍候再试'})


@cache('AccessToken', expire=7200)
async def get_access_token():
    """
    获取 access_token

    :@return: {
        "access_token": string  # 获取到的凭证
        "expires_in": number    # 凭证有效时间，单位：秒。目前是 7200 秒之内的值。
    }
    """
    api = 'https://api.weixin.qq.com/cgi-bin/token'
    params = {
        'appid': MP_APP_ID,
        'secret': MP_APP_SECRET,
        'grant_type': 'client_credential',
    }
    async with httpx.AsyncClient() as cli:
        response = await cli.get(api, params=params)
        if response.status_code == 200:
            result = response.json()
            if result.get('access_token'):
                return AttrDict(result)
            raise SkipCache(AttrDict(result))
        raise SkipCache(AttrDict({'errcode': -1, 'errmsg': '系统繁忙，请稍候再试'}))


async def get_phone_num(code: str, access_token: str):
    """获取手机号"""
    api = f'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={access_token}'
    params = {'code': code}
    async with httpx.AsyncClient() as cli:
        response = await cli.post(api, data=params)
        if response.status_code == 200:
            res = response.json()
            if res['errcode'] == 0:
                return AttrDict(res['phone_info'])
            return AttrDict(res)
        return AttrDict({'errcode': -1, 'errmsg': '系统繁忙，请稍候再试'})


def decrypt_data(encrypted_data, iv, session_key):
    """解密用户数据"""
    # base64 decode
    b_session_key = base64.b64decode(session_key)
    b_encrypted_data = base64.b64decode(encrypted_data)
    b_iv = base64.b64decode(iv)

    # 解密数据
    cipher = AES.new(b_session_key, AES.MODE_CBC, b_iv)
    b_decrypted = cipher.decrypt(b_encrypted_data)
    j_decrypted = unpad(b_decrypted, AES.block_size, style='pkcs7')
    decrypted = AttrDict(json.loads(j_decrypted))  # 将解密后的 JSON 字符串解析为 Python 字典

    # 校验 watermark，确保数据是来自当前小程序的
    if decrypted.get('watermark', {}).get('appid') != MP_APP_ID:
        raise ValueError('AppID mismatch or data tampered.')

    return decrypted


def payment_signature(appid: str, timestamp: str, nonce: str, package: str) -> str:
    """支付签名"""
    sign_values = [appid, timestamp, nonce, package]
    return wxpay.sign(sign_values, SignType.RSA_SHA256)


wxpay = WeChatPay(**WX_PAYMENT)
