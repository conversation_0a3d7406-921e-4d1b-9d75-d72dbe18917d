from contextvars import <PERSON>textV<PERSON>
from typing import Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint


class State:
    """状态"""

    _state_storage: ContextVar[dict[str, Any]] = ContextVar('State')

    @classmethod
    def init(cls):
        """初始化状态"""
        return cls._state_storage.set({})

    @classmethod
    def set(cls, **kwargs):
        """设置状态"""
        stat = cls._state_storage.get()
        stat.update(kwargs)

    @classmethod
    def get(cls, name: str, default=None) -> Any:
        """获取状态"""
        stat = cls.get_all()
        return stat.get(name, default)

    @classmethod
    def get_all(cls) -> dict:
        """获取所有状态"""
        try:
            return cls._state_storage.get()
        except LookupError:
            cls.init()
            return cls._state_storage.get()

    @classmethod
    def reset(cls, token):
        """清空状态"""
        cls._state_storage.reset(token)


class StateMiddleware(BaseHTTPMiddleware):
    """状态中间件"""

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """中间件分发"""
        # Init State
        token = State.init()
        State.set(request=request)

        try:
            return await call_next(request)
        finally:
            # Reset State
            State.reset(token)
