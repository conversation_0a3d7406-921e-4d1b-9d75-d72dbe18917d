#!/bin/bash
LOCAL_DIR=$(readlink -f $(dirname $(dirname "$0")))
SERVER=ant
REMOTE_DIR=/opt/hxmp
DELETE=""

while getopts ":D" opt; do
    case $opt in
        D)
            DELETE="--delete"
            ;;
        \?)
            echo "无效的参数: -$OPTARG" >&2
            echo "Usage: release.sh [-D]" >&2
            exit 1
            ;;
    esac
done
shift $((OPTIND-1))

echo "Syncing Files"
rsync -acvzHP $DELETE \
      --exclude={.git,.venv,.DS_Store,__pycache__,.vscode,.mypy_cache,.gitignore,upload,logs} \
      $LOCAL_DIR/ $SERVER:$REMOTE_DIR/

# ssh $SERVER "
#     printf '\nReloading HuaxiaMP Service\n' &&
#     sudo systemctl reload hxmp.service
# "
echo -e "\nRelease Done"
