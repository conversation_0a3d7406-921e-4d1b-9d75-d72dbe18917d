{% extends "base.html" %}

{% block content %}
  <div class="container-fluid">
    <h2 class="mb-4">系统设置</h2>

    <!-- Alert 消息区域 -->
    <div id="alert-area"></div>

    <!-- 首页轮播图 -->
    <div class="card mb-4 shadow">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-images me-2"></i>首页轮播图
        </h5>
      </div>
      <div class="card-body">
        <div class="row justify-content-around">
          {% for i in range(2) %}
            <div class="col-md-3">
              <div class="text-center">
                {% if settings.slides[i] %}
                  <div class="mb-3 settings-upimg-preview">
                    <img src="{{ settings.slides[i] }}"
                         class="img-fluid w-100 h-100"
                         alt="轮播图{{ i+1 }}" />
                  </div>
                {% else %}
                  <div class="mb-3 settings-slide-placeholder">
                    <span class="text-muted">尚未上传</span>
                  </div>
                {% endif %}
                <button type="button"
                        class="btn btn-primary"
                        onclick="openSlideUpload(`{{ i }}`)">
                  <i class="fas fa-upload me-1"></i>设置「<strong>轮播图 - {{ i+1 }}</strong>」
                </button>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <div class="row my-5">
      <!-- 品牌 Logo 和 背景 -->
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fa-solid fa-award text-primary me-2"></i>品牌 Logo 和 背景
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Logo 上传 -->
              <div class="col-md-6">
                <div class="text-center">
                  {% if settings.logo %}
                    <div class="mb-3 settings-upimg-preview p-3">
                      <img src="{{ UPLOAD_URL }}/{{ LOGO }}"
                           class="img-fluid"
                           alt="Logo"
                           onerror="this.style.display='none';this.nextElementSibling.style.display='block'" />
                    </div>
                  {% else %}
                    <div class="mb-3 settings-slide-placeholder">
                      <span class="text-muted">尚未上传</span>
                    </div>
                  {% endif %}
                  <button type="button"
                          class="btn btn-primary"
                          onclick="openLogoUpload()">
                    <i class="fas fa-upload me-1"></i>设置「<strong>品牌 Logo</strong>」
                  </button>
                </div>
              </div>

              <!-- 背景图上传 -->
              <div class="col-md-6">
                <div class="text-center">
                  {% if settings.brand_bg %}
                    <div class="mb-3 settings-upimg-preview">
                      <img src="{{ UPLOAD_URL }}/{{ BRAND_BG }}"
                           class="img-fluid"
                           alt="背景图"
                           onerror="this.style.display='none';this.nextElementSibling.style.display='block'" />
                    </div>
                  {% else %}
                    <div class="mb-3 settings-slide-placeholder">
                      <span class="text-muted">尚未上传</span>
                    </div>
                  {% endif %}
                  <button type="button"
                          class="btn btn-primary"
                          onclick="openBgUpload()">
                    <i class="fas fa-upload me-1"></i>设置「<strong>品牌背景图</strong>」
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 品牌介绍 -->
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-info-circle text-primary me-2"></i>品牌介绍
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3 settings-text-content">
              {% if settings.brand_intro %}
                <p>
                  {{ settings.brand_intro|replace('\n', '<br />') |safe }}
                </p>
              {% else %}
                <p class="text-muted">暂未设置</p>
              {% endif %}
            </div>
            <div class="text-end">
              <button type="button"
                      class="btn btn-primary"
                      onclick="openTextEditor('brand_intro', '品牌介绍', `{{ settings.brand_intro or '' }}`)">
                <i class="fas fa-edit me-1"></i>修改
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row my-5">
      <!-- 热展推荐 -->
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-star text-warning me-2"></i>热展推荐
            </h5>
          </div>
          <div class="card-body">
            <form id="exhi-rcmds-form">
              <div class="ticket-checkbox-list">
                {% for ticket in tickets %}
                  <div class="form-check mb-2">
                    <input class="form-check-input border-primary-subtle"
                           type="checkbox"
                           value="{{ ticket.id }}"
                           id="exhi_{{ ticket.id }}"
                           {% if settings.exhi_rcmd and ticket.id in settings.exhi_rcmd %}checked{% endif %} />
                    <label class="form-check-label" for="exhi_{{ ticket.id }}">{{ ticket.title }}</label>
                  </div>
                {% endfor %}
              </div>
              <div class="mt-3 text-end">
                <button type="button"
                        class="btn btn-primary"
                        onclick="saveExhiRcmds()">
                  <i class="fas fa-save me-1"></i>确定
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 剧场开关 -->
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fa-solid fa-sliders text-secondary me-2"></i>功能开关
            </h5>
          </div>
          <div class="card-body">
            <div class="form-check form-switch m-3">
              <input id="theater-switch"
                     type="checkbox"
                     class="form-check-input border-primary-subtle"
                     {% if settings.theater_enable %}checked{% endif %}
                     onchange="saveTheaterSwitch(this.checked)" />
              <label class="form-check-label" for="theater-switch">启用空间剧场功能</label>
            </div>
            <div class="form-check form-switch m-3">
              <input id="thirdparty-switch"
                     type="checkbox"
                     class="form-check-input border-primary-subtle"
                     disabled />
              <label class="form-check-label" for="thirdparty-switch">启用第三方平台预约</label>
            </div>
            <div class="form-check form-switch m-3">
              <input id="ar-switch"
                     type="checkbox"
                     class="form-check-input border-primary-subtle"
                     disabled />
              <label class="form-check-label" for="thirdparty-switch">启用 AR 相机功能</label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row my-5">
      <!-- 用户须知 -->
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-exclamation-triangle text-danger me-2"></i>用户须知
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3 settings-text-content">
              {% if settings.user_notice %}
                <p>
                  {{ settings.user_notice|replace('\n', '<br />') |safe }}
                </p>
              {% else %}
                <p class="text-muted">暂未设置</p>
              {% endif %}
            </div>
            <div class="text-end">
              <button type="button"
                      class="btn btn-primary"
                      onclick="openTextEditor('user_notice', '用户须知', `{{ settings.user_notice or '' }}`)">
                <i class="fas fa-edit me-1"></i>修改
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 隐私政策 -->
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-shield-alt text-danger me-2"></i>隐私政策
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3 settings-text-content">
              {% if settings.privacy %}
                <p>
                  {{ settings.privacy|replace('\n', '<br />') |safe }}
                </p>
              {% else %}
                <p class="text-muted">暂未设置</p>
              {% endif %}
            </div>
            <div class="text-end">
              <button type="button"
                      class="btn btn-primary"
                      onclick="openTextEditor('privacy', '隐私政策', `{{ settings.privacy or '' }}`)">
                <i class="fas fa-edit me-1"></i>修改
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 轮播图上传模态框 -->
    <div class="modal fade" id="slideUploadModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">上传轮播图</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="slideUploadForm" enctype="multipart/form-data">
              <input type="hidden" id="slideNum" name="slide_num" />
              <div class="mb-3">
                <label for="slideFile" class="form-label">选择图片文件（仅支持JPG格式）</label>
                <input type="file"
                       class="form-control border-primary-subtle"
                       id="slideFile"
                       name="file"
                       accept=".jpg,.jpeg"
                       required />
              </div>
              <div class="text-muted small">
                <i class="fas fa-info-circle me-1"></i>
                文件大小不能超过5MB，仅支持JPG格式
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal">取消</button>
            <button type="button"
                    class="btn btn-primary"
                    onclick="uploadSlide()">
              <i class="fas fa-upload me-1"></i>上传
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文本编辑模态框 -->
    <div class="modal fade" id="textEditorModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="textEditorTitle">编辑文本</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="textEditorForm">
              <input type="hidden" id="textSettingName" name="name" />
              <input type="hidden" name="vtype" value="text" />
              <div class="mb-3">
                <textarea class="form-control"
                          id="textContent"
                          name="value"
                          rows="10"
                          placeholder="请输入内容..."></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal">取消</button>
            <button type="button"
                    class="btn btn-primary"
                    onclick="saveTextContent()">
              <i class="fas fa-save me-1"></i>保存
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Logo上传模态框 -->
    <div class="modal fade" id="logoUploadModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">上传品牌 Logo</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="logoUploadForm" enctype="multipart/form-data">
              <div class="mb-3">
                <label for="logoFile" class="form-label">选择 Logo 文件（仅支持 PNG 格式）</label>
                <input type="file"
                       class="form-control border-primary-subtle"
                       id="logoFile"
                       name="file"
                       accept=".png"
                       required />
              </div>
              <div class="text-muted small">
                <i class="fas fa-info-circle me-1"></i>
                文件大小不能超过5MB，仅支持PNG格式
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal">取消</button>
            <button type="button"
                    class="btn btn-primary"
                    onclick="uploadLogo()">
              <i class="fas fa-upload me-1"></i>上传
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景图上传模态框 -->
    <div class="modal fade" id="bgUploadModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">上传品牌背景图</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="bgUploadForm" enctype="multipart/form-data">
              <div class="mb-3">
                <label for="bgFile" class="form-label">选择背景图文件（仅支持 JPG 格式）</label>
                <input type="file"
                       class="form-control border-primary-subtle"
                       id="bgFile"
                       name="file"
                       accept=".jpg,.jpeg"
                       required />
              </div>
              <div class="text-muted small">
                <i class="fas fa-info-circle me-1"></i>
                文件大小不能超过5MB，仅支持JPG格式
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="uploadBg()">
              <i class="fas fa-upload me-1"></i>上传
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block ext_js %}
  <script>
    // 打开轮播图上传对话框
    function openSlideUpload(slideNum) {
      document.getElementById('slideNum').value = slideNum;
      new bootstrap.Modal(document.getElementById('slideUploadModal')).show();
    }

    // 上传轮播图
    async function uploadSlide() {
      const form = document.getElementById('slideUploadForm');
      const formData = new FormData(form);

      try {
        const response = await fetch('/adm/settings/upload-slide/', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();
        showAlert('上传成功！', 'success');
        location.reload();
      } catch (error) {
        showAlert('上传失败：' + error.msg, 'danger');
      }
    }

    // 保存热展推荐
    async function saveExhiRcmds() {
      const checkboxes = document.querySelectorAll('#exhi-rcmds-form input[type="checkbox"]:checked');
      const selectedIds = Array.from(checkboxes).map((cb) => parseInt(cb.value));

      const formData = new FormData();
      formData.append('name', 'exhi_rcmd');
      formData.append('value', JSON.stringify(selectedIds));
      formData.append('vtype', 'json');

      try {
        const response = await fetch('/adm/settings/save/', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();
        showAlert('保存成功！', 'success');
      } catch (error) {
        showAlert('保存失败：' + error.message, 'danger');
      }
    }

    // 保存剧场开关
    async function saveTheaterSwitch(checked) {
      const formData = new FormData();
      formData.append('name', 'theater_enable');
      formData.append('value', checked ? 'true' : 'false');
      formData.append('vtype', 'bool');

      try {
        const response = await fetch('/adm/settings/save/', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();
        showAlert('保存成功！', 'success');
      } catch (error) {
        showAlert('保存失败：' + error.message, 'danger');
        // 恢复开关状态
        document.getElementById('theater-switch').checked = !checked;
      }
    }

    // 打开文本编辑器
    function openTextEditor(settingName, title, content) {
      document.getElementById('textSettingName').value = settingName;
      document.getElementById('textEditorTitle').textContent = '编辑' + title;
      document.getElementById('textContent').value = content;
      new bootstrap.Modal(document.getElementById('textEditorModal')).show();
    }

    // 保存文本内容
    async function saveTextContent() {
      const form = document.getElementById('textEditorForm');
      const formData = new FormData(form);

      try {
        const response = await fetch('/adm/settings/save/', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        showAlert('保存成功！', 'success');
        // 延迟 1 秒后刷新页面
        setTimeout(function() { location.reload(); }, 700);
      } catch (error) {
        showAlert('保存失败：' + error.message, 'danger');
      }
    }

    // 打开Logo上传对话框
    function openLogoUpload() {
      new bootstrap.Modal(document.getElementById('logoUploadModal')).show();
    }

    // 打开背景图上传对话框
    function openBgUpload() {
      new bootstrap.Modal(document.getElementById('bgUploadModal')).show();
    }

    // 上传Logo
    async function uploadLogo() {
      const form = document.getElementById('logoUploadForm');
      const formData = new FormData(form);

      try {
        const response = await fetch('/adm/settings/upload-logo/', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        showAlert('上传成功！', 'success');
        location.reload();
      } catch (error) {
        showAlert('上传失败：' + error.message, 'danger');
      }
    }

    // 上传背景图
    async function uploadBg() {
      const form = document.getElementById('bgUploadForm');
      const formData = new FormData(form);

      try {
        const response = await fetch('/adm/settings/upload-bg/', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        showAlert('上传成功！', 'success');
        location.reload();
      } catch (error) {
        showAlert('上传失败：' + error.message, 'danger');
      }
    }

    // 显示Bootstrap Alert
    function showAlert(message, type) {
      const alertHtml = `
        <div class="fixed-top row d-flex justify-content-center z-top">
          <div class="col-md-3 mt-5">
            <div class="alert text-bg-${type} alert-dismissible fade show shadow-lg fw-bold fs-5" role="alert">
              <i class="fa-solid fa-circle-check me-2"></i>${message}
            </div>
          </div>
        </div>
      `;
      const alertArea = document.getElementById('alert-area');
      alertArea.innerHTML = alertHtml; // 替换现有内容

      // 3秒后自动关闭
      setTimeout(() => {
        const alertElement = alertArea.querySelector('.alert');
        if (alertElement) {
          const bsAlert = bootstrap.Alert.getOrCreateInstance(alertElement);
          bsAlert.close();
        }
      }, 3000);
    }
  </script>
{% endblock ext_js %}
