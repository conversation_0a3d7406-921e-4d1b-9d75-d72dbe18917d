{% extends "base.html" %}

{% block content %}
  {% if manager %}
    <h2 class="h2 mb-4">
      修改 <span class="text-primary fw-bold">{{ manager.username }}</span> 的资料
    </h2>
  {% else %}
    <h2 class="h2 mb-4">添加新成员</h2>
  {% endif %}

  <form method="post" action="/adm/manager/save/">
    {% if manager %}<input type="hidden" name="manager_id" value="{{ manager.id }}" />{% endif %}
    <div class="mb-3 col-md-5">
      <input type="hidden"
             name="id"
             value="{{ manager.id if manager else '' }}" />
      <label for="username" class="form-label text-primary fw-bold">用户名</label>
      <input type="text"
             class="form-control border-primary-subtle"
             placeholder="请输入用户名，中、英文都可以"
             id="username"
             name="username"
             value="{{ manager.username if manager else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-5">
      <label for="password" class="form-label text-primary fw-bold">密码</label>
      <input type="password"
             class="form-control border-primary-subtle"
             placeholder="密码必须包含字母、数字和特殊字符，且长度至少 8 位。"
             id="password"
             name="password"
             {% if not manager %}required{% endif %} />
      <div class="form-text">
        {% if manager %}留空则不修改密码。{% endif %}
      </div>
    </div>
    <div class="mb-3 col-md-5">
      <label for="phone" class="form-label">手机号</label>
      <input type="text"
             class="form-control border-primary-subtle"
             placeholder="请输入 11 位手机号"
             id="phone"
             name="phone"
             value="{{ manager.phone if manager else '' }}" />
    </div>
    <div class="mb-3 col-md-5">
      <label for="intro" class="form-label">简介</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="intro"
             name="intro"
             value="{{ manager.intro if manager else '' }}" />
    </div>
    <div class="mb-3 col-md-5">
      <label for="role" class="form-label text-primary fw-bold">角色</label>
      <select class="form-select border-primary-subtle"
              id="role"
              name="role"
              required>
        <option value="">请选择角色</option>
        {% for role in available_roles %}
          <option value="{{ role.value }}"
                  {% if manager and manager.role == role %}selected{% endif %}>{{ role.value }}</option>
        {% endfor %}
      </select>
    </div>
    {% if exhihalls %}
      <div class="mb-3 col-md-5">
        <label for="hid" class="form-label text-primary fw-bold">所属展馆</label>
        <select class="form-select border-primary-subtle"
                id="hid"
                name="hid">
          <option value="">请选择</option>
          {% for hall in exhihalls %}
            <option value="{{ hall.id }}"
                    {% if manager and manager.hid == hall.id %}selected{% endif %}>{{ hall.name }}</option>
          {% endfor %}
        </select>
        <div class="form-text">店长和员工必须选择所属门店</div>
      </div>
    {% endif %}
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/manager/" class="btn btn-secondary">取消</a>
  </form>

  <script>
    // 根据角色选择动态显示/隐藏门店选择
    document.addEventListener('DOMContentLoaded', function() {
      const roleSelect = document.getElementById('role');
      const hallSelect = document.getElementById('hid');
      const hallDiv = hallSelect ? hallSelect.closest('.mb-3') : null;

      function toggleStoreField() {
        const selectedRole = roleSelect.value;
        if (selectedRole === '店长' || selectedRole === '员工') {
          if (hallDiv) {
            hallDiv.style.display = 'block';
            hallSelect.required = true;
          }
        } else {
          if (hallDiv) {
            hallDiv.style.display = 'none';
            hallSelect.required = false;
            hallSelect.value = '';
          }
        }
      }

      // 初始化显示状态
      if (roleSelect && hallSelect) {
        toggleStoreField();

        // 监听角色选择变化
        roleSelect.addEventListener('change', toggleStoreField);
      }
    });
  </script>
{% endblock content %}
