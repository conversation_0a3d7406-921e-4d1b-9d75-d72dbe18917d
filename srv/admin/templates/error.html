{% extends "base.html" %}

{% block content %}
  <div class="container-fluid mt-4">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card text-center shadow-lg">
          <div class="card-header bg-danger text-white">
            <h4 class="mb-0">
              <i class="fas fa-exclamation-triangle me-2"></i>{{ title or '不允许的操作' }}
            </h4>
          </div>
          <div class="card-body">
            <p class="card-text fs-5 text-danger my-5">{{ detail }}</p>
            <hr />
            <p class="text-muted">请检查您的操作是否正确，或联系技术支持。</p>
            <a href="javascript:history.back()" class="btn btn-primary">
              <i class="fas fa-circle-left me-2"></i>返回上一页
            </a>
            <a href="/adm/" class="btn btn-secondary">
              <i class="fas fa-home me-2"></i>返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block ext_js %}
  <script>
    const main = document.getElementsByTagName('main')[0];
    // 添加背景样式 bg-secondary 类
    main.classList.remove('bg-sliver');
    main.classList.add('bg-secondary-subtle');
  </script>
{% endblock ext_js %}
