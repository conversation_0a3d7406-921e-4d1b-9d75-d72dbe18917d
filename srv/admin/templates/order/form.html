{% extends "base.html" %}

{% block content %}
  <h2 class="h2">订单管理</h2>

  <form method="post" action="/adm/order/save/">
    {% if order %}<input type="hidden" name="order_id" value="{{ order.id }}" />{% endif %}
    <div class="mb-3 col-md-6">
      <label for="uid" class="form-label">用户</label>
      <select class="form-select shadow-sm" id="uid" name="uid" required>
        <option disabled value="">请选择用户...</option>
        {% for user in users %}
          <option value="{{ user.id }}"
                  {% if order and user.id == order.uid %}selected{% endif %}>{{ user.name }} (ID: {{ user.id }})</option>
        {% endfor %}
      </select>
    </div>
    <div class="mb-3 col-md-6">
      <label for="tid" class="form-label">展票ID</label>
      <input type="number"
             class="form-control border-primary-subtle"
             id="tid"
             name="tid"
             value="{{ order.tid if order else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="hid" class="form-label">展馆ID</label>
      <input type="number"
             class="form-control border-primary-subtle"
             id="hid"
             name="hid"
             value="{{ order.hid if order else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="phone" class="form-label">手机号</label>
      <input type="tel"
             class="form-control border-primary-subtle"
             id="phone"
             name="phone"
             maxlength="11"
             value="{{ order.phone if order else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="catg" class="form-label">门票类型</label>
      <select class="form-select shadow-sm" id="catg" name="catg">
        {% for c in categories %}
          <option value="{{ c.value }}"
                  {% if order and c.name == order.catg.name %}selected{% endif %}>{{ c.value }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="mb-3 col-md-6">
      <label for="status" class="form-label">订单状态</label>
      <select class="form-select shadow-sm" id="status" name="status">
        {% for s in statuses %}
          <option value="{{ s.value }}"
                  {% if order and s.name == order.status.name %}selected{% endif %}>{{ s.value }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="mb-3 col-md-6">
      <label for="quantity" class="form-label">数量</label>
      <input type="number"
             class="form-control border-primary-subtle"
             id="quantity"
             name="quantity"
             min="1"
             value="{{ order.quantity if order else '1' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="amount" class="form-label">订单金额</label>
      <input type="number"
             step="0.01"
             class="form-control border-primary-subtle"
             id="amount"
             name="amount"
             value="{{ order.amount if order else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="timeslot" class="form-label">场次</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="timeslot"
             name="timeslot"
             maxlength="64"
             value="{{ order.timeslot if order else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="grade" class="form-label">票档</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="grade"
             name="grade"
             maxlength="64"
             value="{{ order.grade if order else '' }}"
             required />
    </div>
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/order/" class="btn btn-secondary">取消</a>
  </form>
{% endblock content %}
