{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">订单数据</h2>
    {% if adm and adm.can_create_orders %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/order/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增订单
        </a>
      </div>
    {% endif %}
  </div>

  <!-- 搜索表单 -->
  <form method="get" class="row g-3 mb-3">
    <div class="col-auto">
      <input type="text"
             name="q"
             class="form-control"
             placeholder="订单号/手机号"
             value="{{ q or '' }}" />
    </div>
    <div class="col-auto">
      <button type="submit" class="btn btn-primary">搜索</button>
    </div>
    <div class="col-auto ms-auto">
      <div class="row g-3">
        {% if halls %}
          <div class="col-auto">
            <select name="hid"
                    id="hid-filter"
                    class="form-select"
                    onchange="this.form.submit()">
              <option value="">所有展馆</option>
              {% for hall in halls %}
                <option value="{{ hall.id }}"
                        {% if hall.id == current_hid %}selected{% endif %}>{{ hall.name }}</option>
              {% endfor %}
            </select>
          </div>
        {% endif %}
        <div class="col-auto">
          <select name="status"
                  id="status-filter"
                  class="form-select"
                  onchange="this.form.submit()">
            <option value="" {% if not current_status %}selected{% endif %}>全部状态</option>
            {% for s in statuses %}
              <option value="{{ s.value }}"
                      {% if s.value == current_status %}selected{% endif %}>{{ s.value }}</option>
            {% endfor %}
          </select>
        </div>
      </div>
    </div>
  </form>

  {% if orders %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm align-middle">
        <thead>
          <tr>
            <th class="px-3">商户订单号</th>
            <th class="px-3">用户手机号</th>
            <th class="px-3">下单时间</th>
            <th class="px-3 text-center">订单状态</th>
            <th class="px-3">所属展馆</th>
            <th class="px-3">场次</th>
            <th class="px-3">票档</th>
            <th class="px-3 text-end">金额 (元)</th>
            {% if adm and adm.can_checkin_orders %}<th class="px-3">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for order in orders %}
            <tr>
              <td class="px-3 font-monospace">
                <a href="/adm/order/detail/?order_id={{ order.id }}">{{ order.trade_no }}</a>
              </td>
              <td class="px-3">
                <a href="/adm/user/detail/?user_id={{ order.uid }}">{{ order.phone }}</a>
              </td>
              <td class="px-3">{{ iso_date(order.created, 'm') }}</td>
              {% if order.status == '待使用' %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-success px-3">{{ order.status }}</span>
                </td>
              {% elif order.status == '待支付' %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-info px-3">{{ order.status }}</span>
                </td>
              {% elif order.status == '已使用' %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-dark px-3">{{ order.status }}</span>
                </td>
              {% else %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-secondary px-3">{{ order.status }}</span>
                </td>
              {% endif %}
              <td class="px-3">{{ order.exhihall.name }}</td>
              <td class="px-3">{{ order.timeslot }}</td>
              <td class="px-3">{{ order.grade }}</td>
              <td class="px-3 text-end">{{ order.amount }}</td>
              <td class="px-3">
                <div class="btn-group">
                  {% if adm and adm.can_checkin_orders and order.status == '待使用' %}
                    <a href="/adm/order/detail/?order_id={{ order.id }}"
                       class="btn btn-sm btn-primary">核销</a>
                  {% endif %}

                  {% if adm and adm.can_delete_orders and cfg.DEBUG %}
                    <a href="/adm/order/form/?order_id={{ order.id }}"
                       class="btn btn-sm btn-warning">修改</a>
                    <a href="/adm/order/delete/?order_id={{ order.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？');">删除</a>
                  {% endif %}
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      暂无订单
    </div>
  {% endif %}

  <!-- 分页 -->
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      {% if page > 1 %}
        <li class="page-item">
          <a class="page-link"
             href="?page={{ page - 1 }}&q={{ q or '' }}&status={{ current_status or '' }}&hid={{ current_hid or '' }}">上一页</a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <a class="page-link" href="#">上一页</a>
        </li>
      {% endif %}

      {% for p in range(1, total_pages + 1) %}
        <li class="page-item {% if p == page %}active{% endif %}">
          <a class="page-link"
             href="?page={{ p }}&q={{ q or '' }}&status={{ current_status or '' }}&hid={{ current_hid or '' }}">{{ p }}</a>
        </li>
      {% endfor %}

      {% if page < total_pages %}
        <li class="page-item">
          <a class="page-link"
             href="?page={{ page + 1 }}&q={{ q or '' }}&status={{ current_status or '' }}&hid={{ current_hid or '' }}">下一页</a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <a class="page-link" href="#">下一页</a>
        </li>
      {% endif %}
    </ul>
  </nav>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const selectElement = document.getElementById('status-filter');
      if (!selectElement) return;

      const statusColorMap = {
        '未支付': 'text-bg-secondary',
        '待使用': 'text-bg-success',
        '已使用': 'text-bg-dark',
        '退款': 'text-bg-warning',
      };

      function applyColor() {
        const selectedValue = selectElement.value;
        // Remove all possible color classes first
        Object.values(statusColorMap).forEach(className => {
          selectElement.classList.remove(className);
        });
        // Add the new class if the selected value has a mapping
        if (statusColorMap[selectedValue]) {
          selectElement.classList.add(statusColorMap[selectedValue]);
        }
      }

      // Apply color on page load
      applyColor();
    });
  </script>
{% endblock content %}
