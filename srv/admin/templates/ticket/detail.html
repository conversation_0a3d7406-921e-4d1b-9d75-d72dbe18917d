{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">
      <span class="text-primary fw-bold">{{ ticket.title }}</span>
    </h2>
    {% if adm and adm.can_manage_tickets %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/ticket/form/?tid={{ ticket.id }}"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-edit"></i>
          修改
        </a>
      </div>
    {% endif %}
  </div>

  <div class="row">
    <div class="col-md-12">
      <table class="table table-striped align-middle">
        <tbody>
          <tr>
            <th scope="row" class="w-25 text-start">标题</th>
            <td class="px-3">{{ ticket.title }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">展馆</th>
            <td class="px-3">
              <a href="/adm/exhihall/detail/?hid={{ ticket.exhihall.id }}">{{ ticket.exhihall.name }}（{{ ticket.exhihall.city }}）</a>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">展览</th>
            <td class="px-3">
              <a href="/adm/exhibition/detail/?eid={{ ticket.exhibition.id }}">{{ ticket.exhibition.name }}</a>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">类型</th>
            <td class="px-3">
              <span class="badge
                           {% if ticket.catg == '预约票' %}
                             bg-success
                           {% else %}
                             bg-primary
                           {% endif %}
                           fs-6">{{ ticket.catg }}</span>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">展期</th>
            <td class="px-3">
              {% if ticket.start and ticket.end %}
                {{ cn_date(ticket.start, 'd') }} ～ {{ cn_date(ticket.end, 'd') }}
              {% elif not ticket.start and ticket.end %}
                至 {{ cn_date(ticket.end, 'd') }} 为止
              {% else %}
                <span class="text-muted">常驻展，无限期</span>
              {% endif %}
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">票价</th>
            <td class="px-3 py-0">
              <table class="table table-hover align-middle mb-0">
                <tbody>
                  {% for timeslot, grades in ticket.prices.items() %}
                    {% for grade, price in grades %}
                      <tr>
                        {% if loop.first %}<td rowspan="{{ grades|length }}">{{ timeslot }}</td>{% endif %}
                        <td>{{ grade }}</td>
                        <td class="text-end font-monospace">¥{{ price }}</td>
                      </tr>
                    {% endfor %}
                  {% endfor %}
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="row mt-5 justify-content-around">
    <div class="col-md-2 text-center">
      <h5>缩略图</h5>
      <hr />
      <img src="{{ ticket.exhibition.thumbnail }}"
           alt="{{ ticket.exhibition.title }}"
           class="img-fluid rounded" />
    </div>
    <div class="col-md-6 text-center">
      <h5>概览图</h5>
      <hr />
      <img src="{{ ticket.exhibition.banner }}"
           alt="{{ ticket.exhibition.title }}"
           class="img-fluid rounded" />
    </div>
  </div>
{% endblock content %}
