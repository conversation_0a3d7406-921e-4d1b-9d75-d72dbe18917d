{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2 text-primary">{{ hall.name }}</h2>
    {% if adm and adm.can_manage_exhibitions %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/exhihall/form/?hid={{ hall.id }}"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-edit"></i>
          修改展馆
        </a>
      </div>
    {% endif %}
  </div>

  <div class="row">
    <div class="col-md-12">
      <table class="table align-middle">
        <tbody>
          <tr>
            <th scope="row" class="w-25 text-start">展馆名称</th>
            <td class="px-3">{{ hall.name }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">所在城市</th>
            <td class="px-3">{{ hall.city }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">展馆地址</th>
            <td class="px-3">{{ hall.addr }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">入馆公告</th>
            <td class="px-3">{{ hall.notice }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">门店 AppID</th>
            <td class="px-3">{{ hall.appid or '-' }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">门店商户号</th>
            <td class="px-3">{{ hall.mchid or '-' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mt-5 mb-3 border-bottom">
    <h5>在售的门票</h5>
    {% if adm and adm.can_manage_exhibitions %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/ticket/form/?hid={{ hall.id }}"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          添加门票
        </a>
      </div>
    {% endif %}
  </div>
  {% if tickets %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm align-middle">
        <thead>
          <tr>
            <th class="px-3 text-center">ID</th>
            <th class="px-3 w-25">标题</th>
            <th class="px-3">展馆</th>
            <th class="px-3">展览</th>
            <th class="px-3 text-end">最低票价</th>
            <th class="px-3 text-end">最高票价</th>
            {% if adm and adm.can_manage_exhibitions %}<th class="px-3">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for ticket in tickets %}
            <tr>
              <td class="px-3 text-center">{{ ticket.id }}</td>
              <td class="px-3">
                <a href="/adm/ticket/detail/?tid={{ ticket.id }}">{{ ticket.title }}</a>
              </td>
              <td class="px-3">{{ ticket.exhihall.name }}</td>
              <td class="px-3">{{ ticket.exhibition.name }}</td>
              <td class="px-3 text-end">
                <strong class="font-monospace">{{ ticket.price_range[0] }}</strong> <span class="text-secondary">¥</span>
              </td>
              <td class="px-3 text-end">
                <strong class="font-monospace text-danger">{{ ticket.price_range[1] }}</strong> <span class="text-secondary">¥</span>
              </td>
              {% if adm and adm.can_manage_exhibitions %}
                <td class="px-3">
                  <div class="btn-group">
                    <a href="/adm/ticket/detail/?tid={{ ticket.id }}"
                       class="btn btn-sm btn-primary">详情</a>
                    <a href="/adm/ticket/form/?tid={{ ticket.id }}&hid={{ hall.id }}"
                       class="btn btn-sm btn-warning">修改</a>
                    <a href="/adm/ticket/delete/?tid={{ ticket.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？');">删除</a>
                  </div>
                </td>
              {% endif %}
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      尚未添加任何门票
    </div>
  {% endif %}

  <!-- 联票 -->
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mt-5 mb-3 border-bottom">
    <h5>在售的联票</h5>
    {% if adm and adm.can_manage_exhibitions %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/joint/form/?hid={{ hall.id }}"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          添加联票
        </a>
      </div>
    {% endif %}
  </div>
  {% if joints %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm align-middle">
        <thead>
          <tr>
            <th class="px-3 text-center">ID</th>
            <th class="px-3 w-25">标题</th>
            <th class="px-3">展馆</th>
            <th class="px-3">包含展票</th>
            <th class="px-3 text-end">价格区间</th>
            {% if adm and adm.can_manage_exhibitions %}<th class="px-3">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for joint in joints %}
            <tr>
              <td class="px-3 text-center">{{ joint.id }}</td>
              <td class="px-3">
                <a href="/adm/joint/detail/?jid={{ joint.id }}">{{ joint.title }}</a>
              </td>
              <td class="px-3">{{ joint.exhihall.name }}</td>
              <td class="px-3">
                <ul class="mb-0">
                  {% for ticket in joint.tickets %}
                    <li class="align-items-center mb-2">
                      <span>{{ ticket.title }}</span>
                    </li>
                  {% endfor %}
                </ul>
              </td>
              <td class="px-3 text-end">
                <strong class="font-monospace">¥{{ joint.price_range[0] }}</strong> - <strong class="font-monospace text-danger">¥{{ joint.price_range[1] }}</strong>
              </td>
              {% if adm and adm.can_manage_exhibitions %}
                <td class="px-3">
                  <div class="btn-group">
                    <a href="/adm/joint/detail/?jid={{ joint.id }}"
                       class="btn btn-sm btn-primary">详情</a>
                    <a href="/adm/joint/form/?jid={{ joint.id }}"
                       class="btn btn-sm btn-warning">编辑</a>
                    <a href="/adm/joint/delete/?jid={{ joint.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？')">删除</a>
                  </div>
                </td>
              {% endif %}
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      尚未添加任何联票
    </div>
  {% endif %}
{% endblock content %}
