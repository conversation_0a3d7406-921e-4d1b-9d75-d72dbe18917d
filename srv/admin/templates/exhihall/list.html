{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">展馆管理</h2>
    {% if adm and adm.can_manage_exhihalls %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/exhihall/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增展馆
        </a>
      </div>
    {% endif %}
  </div>

  {% if halls %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered align-middle table-sm">
        <thead>
          <tr>
            <th class="px-3 text-center">ID</th>
            <th class="px-3 w-25">展馆名称</th>
            <th class="px-3">城市</th>
            <th class="px-3">地址</th>
            {% if adm and adm.can_manage_exhihalls %}<th class="px-3">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for hall in halls %}
            <tr>
              <td class="px-3 text-center">{{ hall.id }}</td>
              <td class="px-3">
                <a href="/adm/exhihall/detail/?hid={{ hall.id }}">{{ hall.name }}</a>
              </td>
              <td class="px-3">{{ hall.city }}</td>
              <td class="px-3">{{ hall.addr }}</td>
              {% if adm and adm.can_manage_exhihalls %}
                <td class="px-3">
                  <div class="btn-group">
                    <a href="/adm/exhihall/detail/?hid={{ hall.id }}"
                       class="btn btn-sm btn-primary">详情</a>
                    <a href="/adm/exhihall/form/?hid={{ hall.id }}"
                       class="btn btn-sm btn-warning">修改</a>
                    <a href="/adm/exhihall/delete/?hid={{ hall.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？');">删除</a>
                  </div>
                </td>
              {% endif %}
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      尚未添加任何展馆信息
    </div>
  {% endif %}
{% endblock content %}
