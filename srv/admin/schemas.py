import string
from datetime import date, datetime
from enum import StrEnum

from fastapi import Form
from pydantic import BaseModel, Field, field_validator

from apps.exhi.schemas import TicketCategory as Catg
from apps.order.models import OrderStatus
from config import PASSWORD_MIN_LENGTH


class Timeslot(StrEnum):
    anytime = '全通票'
    weekday = '平日票'
    early = '早鸟票'
    special = '特惠票'


class Grade(StrEnum):
    single = '单人'
    double = '双人'
    triple = '三人'
    child = '儿童'
    parent_child = '亲子（1大1小）'
    family = '家庭（2大2小）'
    student = '学生'
    soldier = '军人'
    oldman = '老年人'


class ExhiHallForm(BaseModel):
    """展馆表单，用于创建或更新展馆信息"""

    name: str = Field(..., max_length=32)
    city: str = Field(..., max_length=16)
    addr: str = Field(..., max_length=128)
    notice: str = Field('')
    appid: str | None = Field(None, max_length=32)
    mchid: str | None = Field(None, max_length=32)

    @classmethod
    def load(
        cls,
        name: str = Form(..., max_length=32),
        city: str = Form(..., max_length=16),
        addr: str = Form(..., max_length=128),
        notice: str = Form(''),
        appid: str | None = Form(None, max_length=32),
        mchid: str | None = Form(None, max_length=32),
    ):
        return cls(name=name, city=city, addr=addr, notice=notice, appid=appid, mchid=mchid)


class UserForm(BaseModel):
    """用户表单，用于创建或更新用户信息"""

    name: str | None = Field(None, max_length=64)
    openid: str | None = Field(None, max_length=64, min_length=6)
    phone: str | None = Field(None)
    is_adm: bool = Field(False)

    @classmethod
    def load(
        cls,
        name: str | None = Form(None, max_length=64),
        openid: str | None = Form(None, max_length=64, min_length=6),
        phone: str | None = Form(None),
        is_adm: bool = Form(False),
    ):
        return cls(name=name, openid=openid, phone=phone, is_adm=is_adm)

    @field_validator('phone')
    @classmethod
    def check_phone(cls, v: str | None) -> str | None:
        v = v.strip() if isinstance(v, str) else v
        if not v:
            return None
        if len(v) != 11:
            raise ValueError('手机号长度必须为 11 位')
        return v


class OrderForm(BaseModel):
    """订单表单，用于创建或更新订单信息"""

    uid: int = Field(..., gt=0)
    tid: int = Field(..., gt=0)
    hid: int = Field(..., gt=0)
    phone: str = Field(..., max_length=11)
    catg: Catg = Field(default=Catg.ticket)
    status: OrderStatus = Field(default=OrderStatus.pending)
    quantity: int = Field(..., gt=0)
    amount: float = Field(..., gt=0)
    timeslot: str = Field(..., max_length=64)
    grade: str = Field(..., max_length=64)

    @classmethod
    def load(
        cls,
        uid: int = Form(...),
        tid: int = Form(...),
        hid: int = Form(...),
        phone: str = Form(...),
        catg: Catg = Form(default=Catg.ticket),
        status: OrderStatus = Form(default=OrderStatus.pending),
        quantity: int = Form(...),
        amount: float = Form(...),
        timeslot: str = Form(...),
        grade: str = Form(...),
    ):
        return cls(
            uid=uid,
            tid=tid,
            hid=hid,
            phone=phone,
            catg=catg,
            status=status,
            quantity=quantity,
            amount=amount,
            timeslot=timeslot,
            grade=grade,
        )


class AdminForm(BaseModel):
    """管理员表单，用于创建或更新管理员信息"""

    username: str = Field(..., min_length=2, max_length=32)
    password: str | None = Field(None)
    phone: str | None = Field(None)
    intro: str | None = Field(None)

    @classmethod
    def load(
        cls,
        username: str = Form(..., min_length=2, max_length=32),
        password: str | None = Form(None),
        phone: str | None = Form(None),
        intro: str | None = Form(None),
    ):
        return cls(username=username, password=password, phone=phone, intro=intro)

    @field_validator('password')
    @classmethod
    def check_password(cls, v: str | None) -> str | None:
        v = v.strip() if isinstance(v, str) else v
        if not v:
            return None
        if len(v) < PASSWORD_MIN_LENGTH:
            raise ValueError(f'密码长度不能小于 {PASSWORD_MIN_LENGTH} 位')
        if not set(string.ascii_letters).intersection(v):
            raise ValueError('密码必须包含字母')
        if not set(string.digits).intersection(v):
            raise ValueError('密码必须包含数字')
        if not set(string.punctuation).intersection(v):
            raise ValueError('密码必须包含特殊字符')
        return v

    @field_validator('phone')
    @classmethod
    def check_phone(cls, v: str | None) -> str | None:
        v = v.strip() if isinstance(v, str) else v
        if not v:
            return None
        if len(v) != 11:
            raise ValueError('手机号长度必须为 11 位')
        return v


class TicketForm(BaseModel):
    """门票表单，用于创建或更新门票信息"""

    title: str = Field(..., max_length=64)
    hid: int = Field(..., gt=0)
    eid: int = Field(..., gt=0)
    prices: dict[str, list[list]] = Field(default={})
    tid: int | None = Field(None)
    start: date | None = Field(default=None)
    end: date | None = Field(default=None)
    catg: Catg = Field(default=Catg.ticket)

    @field_validator('start', 'end', mode='before')
    @classmethod
    def empty_str_to_none(cls, v: str) -> str | None:
        if not v:
            return None
        return v

    @field_validator('end')
    @classmethod
    def validate_end_date(cls, v: date | None, values) -> date | None:
        """验证结束日期必须大于开始日期"""
        start_date = values.data.get('start')
        if v and start_date and v <= start_date:
            raise ValueError('结束日期必须大于开始日期')
        return v

    @field_validator('prices')
    @classmethod
    def validate_prices(cls, v: dict) -> dict:
        """验证价格数据格式"""
        if not isinstance(v, dict):
            raise ValueError('价格数据必须是字典格式')

        for timeslot, grades in v.items():
            if not isinstance(grades, list):
                raise ValueError(f'场次 {timeslot} 的票档数据必须是列表格式')

            for grade_info in grades:
                if not isinstance(grade_info, list) or len(grade_info) != 2:
                    raise ValueError('票档信息必须是包含两个元素的列表：[票档名称, 价格]')

                grade_name, price = grade_info
                if not isinstance(grade_name, str) or not grade_name.strip():
                    raise ValueError('票档名称不能为空')

                try:
                    float(price)
                except (ValueError, TypeError) as e:
                    raise ValueError(f'价格 {price} 必须是有效的数字') from e

                if float(price) < 0:
                    raise ValueError('价格不能为负数')
        # 排序
        for grades in v.values():
            grades.sort()
        return dict(sorted(v.items()))


class JointForm(BaseModel):
    """联票表单，用于创建或更新联票信息"""

    title: str = Field(..., max_length=64)
    hid: int = Field(..., gt=0)
    tids: list[int] = Field(..., min_length=2)
    prices: dict[str, list[list]] = Field(default={})
    jid: int | None = Field(None)
    start: date | None = Field(default=None)
    end: date | None = Field(default=None)

    @classmethod
    def load_from_json(cls, data: dict):
        """从 JSON 数据加载表单"""
        if data.get('start'):
            data['start'] = datetime.strptime(data['start'], '%Y-%m-%d').date()
        else:
            data['start'] = None
        if data.get('end'):
            data['end'] = datetime.strptime(data['end'], '%Y-%m-%d').date()
        else:
            data['end'] = None
        return cls(**data)

    @field_validator('end')
    @classmethod
    def validate_end_date(cls, v: date | None, values) -> date | None:
        """验证结束日期必须大于开始日期"""
        start_date = values.data.get('start')
        if v and start_date and v <= start_date:
            raise ValueError('结束日期必须大于开始日期')
        return v

    @field_validator('prices')
    @classmethod
    def validate_prices(cls, v: dict) -> dict:
        """验证价格数据格式"""
        if not isinstance(v, dict):
            raise ValueError('价格数据必须是字典格式')

        for timeslot, grades in v.items():
            if not isinstance(grades, list):
                raise ValueError(f'场次 {timeslot} 的票档数据必须是列表格式')

            for grade_info in grades:
                if not isinstance(grade_info, list) or len(grade_info) != 2:
                    raise ValueError('票档信息必须是包含两个元素的列表：[票档名称, 价格]')

                grade_name, price = grade_info
                if not isinstance(grade_name, str) or not grade_name.strip():
                    raise ValueError('票档名称不能为空')

                try:
                    float(price)
                except (ValueError, TypeError) as e:
                    raise ValueError(f'价格 {price} 必须是有效的数字') from e

                if float(price) < 0:
                    raise ValueError('价格不能为负数')
        # 排序
        for grades in v.values():
            grades.sort()
        return dict(sorted(v.items()))
