from datetime import date

from fastapi import Query, Response
from fastapi.responses import JSONResponse
from fastapi.responses import RedirectResponse as Redirect

from admin.http import render_html, router
from admin.permissions import require_ticket_manage
from admin.schemas import TicketForm
from apps.exhi.models import Exhibition, ExhiHall, Ticket
from apps.exhi.schemas import TicketCategory

__all__ = ['ticket_delete', 'ticket_detail', 'ticket_form', 'ticket_list', 'ticket_save']


@router.get('/ticket/')
@require_ticket_manage()
async def ticket_list() -> Response:
    """展览列表页"""
    tickets = [await e.prefetch() for e in await Ticket.all()]
    return render_html('ticket/list.html', {'tickets': tickets})


@router.get('/ticket/detail/')
async def ticket_detail(tid: int) -> Response:
    """展览详情页"""
    ticket = await Ticket.get(id=tid)
    await ticket.prefetch()
    return render_html('ticket/detail.html', {'ticket': ticket})


@router.get('/ticket/form/')
@require_ticket_manage()
async def ticket_form(tid: int | None = Query(None), hid: int | None = Query(None)) -> Response:
    """展览创建和编辑页"""
    ticket, is_edit = (await Ticket.get_or_none(id=tid), True) if tid else (None, False)
    if ticket:
        hid = ticket.hid
    halls = [await ExhiHall.get(id=hid)] if hid else await ExhiHall.all()
    exhibitions = await Exhibition.all()
    return render_html(
        'ticket/form.html',
        {
            'ticket': ticket,
            'is_edit': is_edit,
            'halls': halls,
            'exhibitions': exhibitions,
            'today': date.today().isoformat(),
            'ticket_categories': list(TicketCategory),
        },
    )


@router.post('/ticket/save/')
@require_ticket_manage()
async def ticket_save(form: TicketForm) -> Response:
    """处理展览创建和编辑表单"""
    update_data = {
        'title': form.title,
        'hid': form.hid,
        'eid': form.eid,
        'prices': form.prices,
        'start': form.start,
        'end': form.end,
        'catg': form.catg,
    }

    if form.tid:
        await Ticket.filter(id=form.tid).update(**update_data)
        await Ticket.clear_cache(form.tid)
        tid = form.tid
    else:
        ticket = await Ticket.create(**update_data)  # type: ignore
        tid = ticket.id

    return JSONResponse({'next_url': f'/adm/ticket/detail/?tid={tid}'})


@router.get('/ticket/delete/')
@require_ticket_manage()
async def ticket_delete(tid: int) -> Response:
    """删除指定展览"""
    await (await Ticket.get(id=tid)).delete()
    return Redirect(url='/adm/ticket/', status_code=303)
