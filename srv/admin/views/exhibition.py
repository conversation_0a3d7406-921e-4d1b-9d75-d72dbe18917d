import json

from fastapi import File, Form, HTTPException, Query, Response, UploadFile
from fastapi.responses import RedirectResponse as Redirect

from admin.http import render_html, router
from apps.exhi.models import Exhibition
from config import ALLOWED_TYPES, BANNER_PREFIX, DETAIL_PREFIX, MAX_SIZE, THUMBNAIL_PREFIX
from libs.utils import file_type, save_upfile

__all__ = ['exhibition_delete', 'exhibition_detail', 'exhibition_form', 'exhibition_list', 'exhibition_save']


@router.get('/exhibition/')
async def exhibition_list() -> Response:
    """展览列表页"""
    exhibitions = await Exhibition.all()
    return render_html('exhibition/list.html', {'exhibitions': exhibitions})


@router.get('/exhibition/detail/')
async def exhibition_detail(eid: int) -> Response:
    """展览详情页"""
    exhibition = await Exhibition.get(id=eid)
    return render_html('exhibition/detail.html', {'exhibition': exhibition})


@router.get('/exhibition/form/')
async def exhibition_form(eid: int | None = Query(None)) -> Response:
    """展览创建和编辑页"""
    exhibition, is_edit = (await Exhibition.get_or_none(id=eid), True) if eid else (None, False)
    return render_html('exhibition/form.html', {'exhibition': exhibition, 'is_edit': is_edit})


@router.post('/exhibition/save/')
async def exhibition_save(
    name: str = Form(..., max_length=32),
    thumbnail: UploadFile = File(...),
    banner: UploadFile = File(...),
    detail: list[UploadFile] = File([]),
    eid: int | None = Form(None),
    thumbnail_path: str | None = Form(None),
    banner_path: str | None = Form(None),
) -> Response:
    """处理展览创建和编辑表单"""
    for upfile in [thumbnail, banner, *detail]:
        if not upfile or not upfile.filename:
            continue
        extension = file_type(upfile)
        if extension not in ALLOWED_TYPES:
            raise HTTPException(status_code=400, detail=f'文件类型 {extension} 不支持，仅支持 jpg/png')
        fsz = upfile.size or 0
        if not (0 < fsz <= MAX_SIZE):
            raise HTTPException(
                status_code=400, detail=f'文件 {upfile.filename} 大小超过限制: {round(fsz / 1024 / 1024, 1)}MB'
            )
        upfile.extension = extension  # type: ignore

    thumbnail_url = (
        await save_upfile(thumbnail, THUMBNAIL_PREFIX) if thumbnail and thumbnail.filename else thumbnail_path
    )
    banner_url = await save_upfile(banner, BANNER_PREFIX) if banner and banner.filename else banner_path
    detail_urls = []
    for i, file in enumerate(detail, start=1):
        if file and file.filename:
            detail_urls.append(await save_upfile(file, DETAIL_PREFIX, i))

    update_data = {
        'name': name,
        'thumbnail': thumbnail_url,
        'banner': banner_url,
    }
    if detail_urls:
        update_data['detail'] = json.dumps(detail_urls)

    if eid:
        await Exhibition.filter(id=eid).update(**update_data)
        await Exhibition.clear_cache(eid)
    else:
        eid = (await Exhibition.create(**update_data)).id  # type: ignore

    return Redirect(url=f'/adm/exhibition/detail/?eid={eid}', status_code=303)


@router.get('/exhibition/delete/')
async def exhibition_delete(eid: int) -> Response:
    """删除指定展览"""
    await (await Exhibition.get(id=eid)).delete()
    return Redirect(url='/adm/exhibition/', status_code=303)
