from fastapi import Depends, Form, HTTPException, Response
from fastapi.responses import RedirectResponse as Redirect

from admin import schemas as sch
from admin.http import render_html, router
from admin.models import Admin, Role
from admin.permissions import filter_exhihalls_by_permission, require_admin_manage
from apps.exhi.models import ExhiHall
from libs.state import State

__all__ = ['manager_delete', 'manager_form', 'manager_list', 'manager_save']


@router.get('/manager/')
@require_admin_manage()
async def manager_list() -> Response:
    """管理员用户列表页"""
    cur_adm = State.get('user')

    # 根据用户角色过滤管理员列表
    if cur_adm.role in [Role.SUPER, Role.ADMIN]:
        managers = await Admin.all()
    elif cur_adm.role == Role.MANAGER:
        # 店长只能看到自己门店的员工和自己
        managers = await Admin.filter(hid=cur_adm.hid, role__in=[Role.MANAGER, Role.STAFF])
    else:
        # 员工和运营只能看到自己
        managers = [cur_adm]

    return render_html('manager/list.html', {'managers': managers, 'cur_adm': cur_adm})


@router.get('/manager/form/')
@require_admin_manage()
async def manager_form(manager_id: int | None = None) -> Response:
    """管理员用户创建和编辑页"""
    cur_adm = State.get('user')

    # 获取可用的门店列表
    hids = await filter_exhihalls_by_permission(cur_adm)
    exhihalls = await ExhiHall.filter(id__in=hids) if hids else []

    manager = None
    is_edit = False

    if manager_id:
        manager = await Admin.get_or_none(id=manager_id)
        if not manager:
            raise HTTPException(status_code=404, detail='管理员不存在')

        # 检查是否有权限编辑此管理员
        if cur_adm.role == Role.MANAGER:
            if manager.hid != cur_adm.hid:
                raise HTTPException(status_code=403, detail='无权限编辑此管理员')

        is_edit = True

    context = {
        'manager': manager,
        'is_edit': is_edit,
        'exhihalls': exhihalls,
        'available_roles': [cur_adm.role] if manager_id == cur_adm.id else cur_adm.can_create_roles,
        'cur_adm': cur_adm,
        'Role': Role,
    }

    return render_html('manager/form.html', context)


@router.post('/manager/save/')
@require_admin_manage()
async def manager_save(
    form: sch.AdminForm = Depends(sch.AdminForm.load),
    manager_id: int | None = Form(None),
    role: Role = Form(...),
    hid: int | None = Form(None),
) -> Response:
    """处理管理员用户创建和编辑表单"""
    cur_adm = State.get('user')

    # 检查是否有权限创建/编辑此角色
    if not cur_adm.can_create_role(role):
        raise HTTPException(status_code=403, detail='无权限创建此角色的账号')

    # 检查门店权限
    if role in [Role.MANAGER, Role.STAFF]:
        if not hid:
            raise HTTPException(status_code=400, detail='店长和员工必须指定门店')

        # 检查是否有权限管理此门店
        if not cur_adm.can_manage_exhihall(hid):
            raise HTTPException(status_code=403, detail='无权限管理此门店')

    # 准备更新数据
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    update_data['role'] = role
    update_data['hid'] = hid

    if form.password:
        update_data['password'] = Admin.hash_password(form.password)

    if manager_id:
        # 编辑现有管理员
        manager = await Admin.get_or_none(id=manager_id)
        if not manager:
            raise HTTPException(status_code=404, detail='管理员不存在')

        # 检查权限
        if cur_adm.role == Role.MANAGER:
            if manager.hid != cur_adm.hid:
                raise HTTPException(status_code=403, detail='无权限编辑此管理员')

        await Admin.filter(id=manager_id).update(**update_data)
        await Admin.clear_cache(manager_id)
    else:
        # 创建新管理员
        await Admin.create(**update_data)

    return Redirect(url='/adm/manager/', status_code=303)


@router.get('/manager/delete/')
@require_admin_manage()
async def manager_delete(manager_id: int) -> Response:
    """删除指定管理员用户"""
    cur_adm = State.get('user')

    manager = await Admin.get_or_none(id=manager_id)
    if not manager:
        raise HTTPException(status_code=404, detail='管理员不存在')

    # 不能删除自己
    if manager.id == cur_adm.id:
        raise HTTPException(status_code=400, detail='不能删除自己')

    # 检查权限
    if cur_adm.role == Role.MANAGER:
        if manager.hid != cur_adm.hid or manager.role != Role.STAFF:
            raise HTTPException(status_code=403, detail='店长只能删除自己门店的员工')
    elif cur_adm.role == Role.ADMIN:
        if manager.role == Role.SUPER:
            raise HTTPException(status_code=403, detail='普通管理员不能删除超级管理员')

    await manager.delete()
    return Redirect(url='/adm/manager/', status_code=303)
